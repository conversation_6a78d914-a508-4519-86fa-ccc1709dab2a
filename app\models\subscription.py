from datetime import datetime, timedelta
from app.extensions import db
import json

class SubscriptionPlan(db.Model):
    """
    Subscription plan model defining different service tiers
    """
    __tablename__ = 'subscription_plan'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    code = db.Column(db.String(20), unique=True, nullable=False)  # basic, standard, premium
    price_monthly = db.Column(db.Float, nullable=False)
    price_yearly = db.Column(db.Float, nullable=False)
    max_devices = db.Column(db.Integer, nullable=False)
    max_technicians = db.Column(db.Integer, nullable=False)
    features = db.Column(db.JSON, default=list)
    is_active = db.Column(db.<PERSON>, default=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<SubscriptionPlan {self.name}>'
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'price_monthly': self.price_monthly,
            'price_yearly': self.price_yearly,
            'max_devices': self.max_devices,
            'max_technicians': self.max_technicians,
            'features': self.features,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class TenantSubscription(db.Model):
    """
    Tenant subscription tracking
    """
    __tablename__ = 'tenant_subscription'
    
    id = db.Column(db.Integer, primary_key=True)
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenant.id'), nullable=False)
    plan_id = db.Column(db.Integer, db.ForeignKey('subscription_plan.id'), nullable=False)
    status = db.Column(db.String(20), default='active')  # active, cancelled, expired
    billing_cycle = db.Column(db.String(20), nullable=False)  # monthly, yearly
    start_date = db.Column(db.DateTime, nullable=False)
    end_date = db.Column(db.DateTime, nullable=False)
    trial_end_date = db.Column(db.DateTime)
    stripe_subscription_id = db.Column(db.String(100))
    stripe_customer_id = db.Column(db.String(100))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships - using string references to avoid circular imports
    plan = db.relationship('SubscriptionPlan', backref=db.backref('subscriptions', lazy=True))
    
    def __repr__(self):
        return f'<TenantSubscription {self.id}>'
    
    @property
    def is_active(self):
        """Check if subscription is active"""
        now = datetime.utcnow()
        return (
            self.status == 'active' and
            self.start_date <= now <= self.end_date
        )
    
    @property
    def is_trial(self):
        """Check if subscription is in trial period"""
        now = datetime.utcnow()
        return (
            self.trial_end_date and
            now <= self.trial_end_date
        )
    
    @property
    def days_remaining(self):
        """Get days remaining in subscription"""
        now = datetime.utcnow()
        if not self.is_active:
            return 0
        return (self.end_date - now).days
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'tenant_id': self.tenant_id,
            'plan': self.plan.to_dict() if self.plan else None,
            'status': self.status,
            'billing_cycle': self.billing_cycle,
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat(),
            'trial_end_date': self.trial_end_date.isoformat() if self.trial_end_date else None,
            'is_active': self.is_active,
            'is_trial': self.is_trial,
            'days_remaining': self.days_remaining,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
