"""
Enhanced Tenant-Aware Base Model
Provides automatic tenant filtering and security for all models
"""

from flask import g
from sqlalchemy.orm import Query
from app.extensions import db
from app.middleware.tenant_context import get_current_tenant, TenantSecurityError
import logging
from sqlalchemy.orm import declared_attr

# Setup logging
model_logger = logging.getLogger('tenant_models')

class TenantAwareQuery(Query):
    """Custom query class that automatically filters by tenant"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._tenant_filtered = False
    
    def filter_by_tenant(self, tenant_id=None):
        """Explicitly filter by tenant ID"""
        if tenant_id is None:
            current_tenant = get_current_tenant()
            if current_tenant:
                tenant_id = current_tenant.id
            else:
                # Return empty query if no tenant context
                return self.filter(False)
        
        self._tenant_filtered = True
        return self.filter(self.column_descriptions[0]['type'].tenant_id == tenant_id)
    
    def all(self):
        """Override all() to ensure tenant filtering"""
        if not self._tenant_filtered and self._should_filter_by_tenant():
            return self.filter_by_tenant().all()
        return super().all()
    
    def first(self):
        """Override first() to ensure tenant filtering"""
        if not self._tenant_filtered and self._should_filter_by_tenant():
            return self.filter_by_tenant().first()
        return super().first()
    
    def one(self):
        """Override one() to ensure tenant filtering"""
        if not self._tenant_filtered and self._should_filter_by_tenant():
            return self.filter_by_tenant().one()
        return super().one()
    
    def one_or_none(self):
        """Override one_or_none() to ensure tenant filtering"""
        if not self._tenant_filtered and self._should_filter_by_tenant():
            return self.filter_by_tenant().one_or_none()
        return super().one_or_none()
    
    def _should_filter_by_tenant(self):
        """Check if this query should be filtered by tenant"""
        if not self.column_descriptions:
            return False
        
        model_class = self.column_descriptions[0]['type']
        return (hasattr(model_class, 'tenant_id') and 
                hasattr(model_class, '__tenant_aware__') and 
                model_class.__tenant_aware__)

class TenantAwareMixin:
    """Enhanced mixin for tenant-aware models"""
    
    # Mark this model as tenant-aware
    __tenant_aware__ = True
    
    # Use custom query class
    query_class = TenantAwareQuery
    
    # Tenant foreign key
    @declared_attr
    def tenant_id(cls):
        return db.Column(
            db.Integer, 
            db.ForeignKey('tenant.id', ondelete='CASCADE'), 
            nullable=False,
            index=True,
            default=lambda: TenantAwareMixin._get_current_tenant_id()
        )
    
    def __init__(self, *args, **kwargs):
        # Set tenant_id if not provided
        if 'tenant_id' not in kwargs:
            current_tenant = get_current_tenant()
            if current_tenant:
                kwargs['tenant_id'] = current_tenant.id
            else:
                # For backward compatibility, try to get from session
                if hasattr(db.session, 'tenant_id') and db.session.tenant_id:
                    kwargs['tenant_id'] = db.session.tenant_id
                else:
                    raise TenantSecurityError("No tenant context available for model creation")
        
        super().__init__(*args, **kwargs)
    
    @staticmethod
    def _get_current_tenant_id():
        """Get current tenant ID for default value"""
        current_tenant = get_current_tenant()
        if current_tenant:
            return current_tenant.id
        
        # Fallback to session tenant_id
        if hasattr(db.session, 'tenant_id') and db.session.tenant_id:
            return db.session.tenant_id
        
        # For development/testing, allow None (will be caught by validation)
        return None
    
    @classmethod
    def query_by_tenant(cls, tenant_id=None):
        """Explicitly query by tenant ID"""
        if tenant_id is None:
            current_tenant = get_current_tenant()
            if not current_tenant:
                return cls.query.filter(False)  # Empty query
            tenant_id = current_tenant.id
        
        return cls.query.filter_by(tenant_id=tenant_id)
    
    @classmethod
    def get_by_id(cls, id, tenant_id=None):
        """Get record by ID within tenant context"""
        if tenant_id is None:
            current_tenant = get_current_tenant()
            if not current_tenant:
                return None
            tenant_id = current_tenant.id
        
        return cls.query.filter_by(id=id, tenant_id=tenant_id).first()
    
    @classmethod
    def get_all_for_tenant(cls, tenant_id=None):
        """Get all records for a specific tenant"""
        return cls.query_by_tenant(tenant_id).all()
    
    @classmethod
    def count_for_tenant(cls, tenant_id=None):
        """Count records for a specific tenant"""
        return cls.query_by_tenant(tenant_id).count()
    
    def save(self):
        """Save with tenant validation"""
        self._validate_tenant_access()
        db.session.add(self)
        db.session.commit()
        return self
    
    def update(self, **kwargs):
        """Update with tenant validation"""
        self._validate_tenant_access()
        
        # Don't allow tenant_id changes
        if 'tenant_id' in kwargs:
            raise TenantSecurityError("Cannot change tenant_id of existing record")
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        
        db.session.commit()
        return self
    
    def delete(self):
        """Delete with tenant validation"""
        self._validate_tenant_access()
        db.session.delete(self)
        db.session.commit()
    
    def _validate_tenant_access(self):
        """Validate that current user can access this record"""
        current_tenant = get_current_tenant()
        if not current_tenant:
            raise TenantSecurityError("No tenant context available")
        
        if self.tenant_id != current_tenant.id:
            model_logger.warning(
                f"Tenant access violation: {current_tenant.id} tried to access "
                f"record from tenant {self.tenant_id}"
            )
            raise TenantSecurityError("Access denied: Record belongs to different tenant")
    
    def to_dict(self, include_tenant=False):
        """Convert to dictionary, optionally including tenant info"""
        result = {}
        for column in self.__table__.columns:
            if column.name == 'tenant_id' and not include_tenant:
                continue
            result[column.name] = getattr(self, column.name)
        return result

# Event listeners will be registered when the app is created
def register_tenant_event_listeners(app):
    """Register tenant validation event listeners"""
    from sqlalchemy import event

    @event.listens_for(db.session, 'before_insert')
    def validate_tenant_on_insert(mapper, connection, target):
        """Validate tenant context before insert"""
        if hasattr(target, '__tenant_aware__') and target.__tenant_aware__:
            if not hasattr(target, 'tenant_id') or target.tenant_id is None:
                current_tenant = get_current_tenant()
                if current_tenant:
                    target.tenant_id = current_tenant.id
                else:
                    raise TenantSecurityError(
                        f"Cannot insert {target.__class__.__name__} without tenant context"
                    )

    @event.listens_for(db.session, 'before_update')
    def validate_tenant_on_update(mapper, connection, target):
        """Validate tenant context before update"""
        if hasattr(target, '__tenant_aware__') and target.__tenant_aware__:
            current_tenant = get_current_tenant()
            if current_tenant and target.tenant_id != current_tenant.id:
                raise TenantSecurityError(
                    f"Cannot update {target.__class__.__name__} from different tenant"
                )

    @event.listens_for(db.session, 'before_delete')
    def validate_tenant_on_delete(mapper, connection, target):
        """Validate tenant context before delete"""
        if hasattr(target, '__tenant_aware__') and target.__tenant_aware__:
            current_tenant = get_current_tenant()
            if current_tenant and target.tenant_id != current_tenant.id:
                raise TenantSecurityError(
                    f"Cannot delete {target.__class__.__name__} from different tenant"
                )

# Utility functions
def create_tenant_aware_model(model_class, **kwargs):
    """Create a new tenant-aware model instance"""
    current_tenant = get_current_tenant()
    if not current_tenant:
        raise TenantSecurityError("No tenant context available")
    
    kwargs['tenant_id'] = current_tenant.id
    return model_class(**kwargs)

def bulk_create_for_tenant(model_class, records_data, tenant_id=None):
    """Bulk create records for a specific tenant"""
    if tenant_id is None:
        current_tenant = get_current_tenant()
        if not current_tenant:
            raise TenantSecurityError("No tenant context available")
        tenant_id = current_tenant.id
    
    records = []
    for data in records_data:
        data['tenant_id'] = tenant_id
        records.append(model_class(**data))
    
    db.session.bulk_save_objects(records)
    db.session.commit()
    return records
