from datetime import datetime, timedelta
from sqlalchemy.orm import declared_attr
from app.extensions import db
from app.models.tenant_aware_base import TenantAwareMixin
from app.models.settings import SystemSettings

class Device(db.Model, TenantAwareMixin):
    id = db.Column(db.Integer, primary_key=True)
    type = db.Column(db.String(100), nullable=False)
    model = db.Column(db.String(100), nullable=False)
    color = db.Column(db.String(50))
    serial_number = db.Column(db.String(100))
    password = db.Column(db.String(100))
    status = db.Column(db.String(20), default='receiving')  # receiving, active, decommissioned, etc.
    owner_name = db.Column(db.String(100))  # حقل لاسم صاحب الجهاز
    notes = db.Column(db.Text)  # حقل للملاحظات (مثل رقم هاتف المالك)

    @declared_attr
    def customer_id(cls):
        return db.Column(db.Integer, db.<PERSON><PERSON><PERSON>('customer.id', name='fk_device_customer'))

    @declared_attr
    def technician_id(cls):
        return db.Column(db.Integer, db.ForeignKey('user.id', name='fk_device_technician'))

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Automatic cleanup fields
    repair_completed_at = db.Column(db.DateTime, nullable=True)  # When repair was marked as completed
    scheduled_for_deletion = db.Column(db.DateTime, nullable=True)  # When device will be auto-deleted
    is_soft_deleted = db.Column(db.Boolean, default=False)  # Soft delete flag
    soft_deleted_at = db.Column(db.DateTime, nullable=True)  # When device was soft deleted
    deletion_reason = db.Column(db.String(100), nullable=True)  # Reason for deletion (auto, manual, etc.)

    # Relationships
    repair_tickets = db.relationship('RepairTicket', back_populates='device', lazy=True)
    # Note: customer and technician relationships are defined in User model with backref

    def __repr__(self):
        return f'<Device {self.type} {self.model}>'

    def mark_repair_completed(self):
        """Mark device repair as completed and schedule for automatic deletion"""
        if not self.repair_completed_at:
            self.repair_completed_at = datetime.utcnow()
            # Schedule deletion for 3 months from completion
            self.scheduled_for_deletion = self.repair_completed_at + timedelta(days=90)

    def is_eligible_for_deletion(self):
        """Check if device is eligible for automatic deletion"""
        if not self.scheduled_for_deletion:
            return False
        return datetime.utcnow() >= self.scheduled_for_deletion and not self.is_soft_deleted

    def days_until_deletion(self):
        """Calculate days remaining until automatic deletion"""
        if not self.scheduled_for_deletion:
            return None
        delta = self.scheduled_for_deletion - datetime.utcnow()
        return max(0, delta.days)

    def soft_delete(self, reason="automatic_cleanup"):
        """Soft delete the device"""
        self.is_soft_deleted = True
        self.soft_deleted_at = datetime.utcnow()
        self.deletion_reason = reason

    def restore_from_soft_delete(self):
        """Restore device from soft delete"""
        self.is_soft_deleted = False
        self.soft_deleted_at = None
        self.deletion_reason = None

    @property
    def deletion_status(self):
        """Get human-readable deletion status"""
        if self.is_soft_deleted:
            return "Soft Deleted"
        elif self.scheduled_for_deletion:
            days_left = self.days_until_deletion()
            if days_left == 0:
                return "Scheduled for deletion today"
            elif days_left is not None:
                return f"Scheduled for deletion in {days_left} days"
        return "Active"

    def to_dict(self):
        """Convert device object to dictionary for JSON serialization"""
        try:
            # Safely get customer name
            customer_name = None
            try:
                customer_name = self.customer.full_name if self.customer else None
            except:
                customer_name = None

            # Safely get technician name
            technician_name = None
            try:
                technician_name = self.technician.full_name if self.technician else None
            except:
                technician_name = None

            return {
                'id': self.id,
                'type': self.type,
                'model': self.model,
                'color': self.color,
                'serial_number': self.serial_number,
                'status': self.status,
                'owner_name': self.owner_name,
                'notes': self.notes,
                'customer_id': self.customer_id,
                'technician_id': self.technician_id,
                'created_at': self.created_at.isoformat() if self.created_at else None,
                'updated_at': self.updated_at.isoformat() if self.updated_at else None,
                'repair_completed_at': self.repair_completed_at.isoformat() if self.repair_completed_at else None,
                'scheduled_for_deletion': self.scheduled_for_deletion.isoformat() if self.scheduled_for_deletion else None,
                'is_soft_deleted': self.is_soft_deleted,
                'deletion_status': self.deletion_status,
                'customer_name': customer_name,
                'technician_name': technician_name
            }
        except Exception as e:
            # Fallback to basic data if there's any error
            return {
                'id': self.id,
                'type': self.type,
                'model': self.model,
                'status': self.status,
                'error': f'Serialization error: {str(e)}'
            }

class UsedPart(db.Model, TenantAwareMixin):
    __tablename__ = 'used_parts'

    id = db.Column(db.Integer, primary_key=True)
    repair_ticket_id = db.Column(db.Integer, db.ForeignKey('repair_ticket.id'), nullable=False)
    part_id = db.Column(db.Integer, db.ForeignKey('spare_parts.id'), nullable=False)
    quantity = db.Column(db.Integer, default=1)

    # Relationship with explicit foreign_keys parameter
    part = db.relationship('SparePart', foreign_keys=[part_id])

    def __repr__(self):
        return f'<UsedPart {self.part.name if self.part else "Unknown"} x{self.quantity}>'

class DeviceDeletionLog(db.Model, TenantAwareMixin):
    """Log table for tracking device deletions for audit purposes"""
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.Integer, nullable=False)  # Original device ID
    device_type = db.Column(db.String(100), nullable=False)
    device_model = db.Column(db.String(100), nullable=False)
    device_serial = db.Column(db.String(100), nullable=True)
    customer_name = db.Column(db.String(100), nullable=True)
    technician_name = db.Column(db.String(100), nullable=True)
    deletion_type = db.Column(db.String(20), nullable=False)  # 'soft', 'hard', 'automatic'
    deletion_reason = db.Column(db.String(200), nullable=True)
    repair_completed_at = db.Column(db.DateTime, nullable=True)
    scheduled_deletion_date = db.Column(db.DateTime, nullable=True)
    actual_deletion_date = db.Column(db.DateTime, default=datetime.utcnow)
    deleted_by_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    repair_tickets_count = db.Column(db.Integer, default=0)
    total_repair_cost = db.Column(db.Float, default=0.0)

    # Additional metadata stored as JSON-like text
    device_metadata = db.Column(db.Text, nullable=True)  # JSON string of additional device data

    def __repr__(self):
        return f'<DeviceDeletionLog {self.device_type} {self.device_model} - {self.deletion_type}>'