{% extends 'base.html' %}

{% block title %}Inventory Integration - Accounting{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-chart-line me-2"></i>Inventory Integration</h1>
        <a href="{{ url_for('accounting.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Accounting
        </a>
    </div>

    <!-- Financial Overview Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${{ "%.2f"|format(total_inventory_value) }}</h4>
                            <p class="mb-0">Total Inventory Value</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${{ "%.2f"|format(potential_profit) }}</h4>
                            <p class="mb-0">Potential Profit</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${{ "%.2f"|format(parts_revenue) }}</h4>
                            <p class="mb-0">Parts Revenue</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-cogs fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${{ "%.2f"|format(low_stock_value) }}</h4>
                            <p class="mb-0">Low Stock Value</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Analysis -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar me-2"></i>Category-wise Financial Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Total Value</th>
                                    <th>Total Cost</th>
                                    <th>Potential Profit</th>
                                    <th>Parts Count</th>
                                    <th>Total Quantity</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for analysis in category_analysis %}
                                <tr>
                                    <td><strong>{{ analysis.category or 'Uncategorized' }}</strong></td>
                                    <td class="text-success">${{ "%.2f"|format(analysis.total_value or 0) }}</td>
                                    <td class="text-danger">${{ "%.2f"|format(analysis.total_cost or 0) }}</td>
                                    <td class="text-info">${{ "%.2f"|format((analysis.total_value or 0) - (analysis.total_cost or 0)) }}</td>
                                    <td>{{ analysis.part_count }}</td>
                                    <td>{{ analysis.total_quantity }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Part Usage -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-history me-2"></i>Recent Part Usage</h5>
                </div>
                <div class="card-body">
                    {% if recent_part_usage %}
                    <div class="list-group list-group-flush">
                        {% for usage in recent_part_usage %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ usage.spare_part.name }}</h6>
                                <small>{{ usage.created_at.strftime('%m/%d') }}</small>
                            </div>
                            <p class="mb-1">Qty: {{ usage.quantity }} - ${{ "%.2f"|format(usage.quantity * usage.spare_part.price) }}</p>
                            <small>Repair #{{ usage.repair_ticket.id if usage.repair_ticket else 'N/A' }}</small>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No recent part usage recorded.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0 !important;
}

.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.list-group-item {
    border: none;
    border-bottom: 1px solid #eee;
}

.list-group-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}
