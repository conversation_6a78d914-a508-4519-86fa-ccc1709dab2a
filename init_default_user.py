#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء مستخدم افتراضي للنظام
"""

import os
import sys
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.user import User
from app.models.settings import SystemSettings

def create_default_user():
    """إنشاء مستخدم افتراضي"""
    app = create_app()
    
    with app.app_context():
        try:
            # Check if admin user already exists
            admin_user = User.query.filter_by(username='admin').first()
            
            if not admin_user:
                # Create default admin user
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='System Administrator',
                    role='admin',
                    is_active=True,
                    created_at=datetime.utcnow()
                )
                admin_user.set_password('admin123')  # Default password
                
                db.session.add(admin_user)
                db.session.commit()
                
                print("✅ Default admin user created successfully!")
                print("   Username: admin")
                print("   Password: admin123")
                print("   Email: <EMAIL>")
            else:
                print("ℹ️ Admin user already exists")
                print(f"   Username: {admin_user.username}")
                print(f"   Email: {admin_user.email}")
            
            # Create default technician user
            tech_user = User.query.filter_by(username='technician').first()
            
            if not tech_user:
                tech_user = User(
                    username='technician',
                    email='<EMAIL>',
                    full_name='Default Technician',
                    role='technician',
                    is_active=True,
                    created_at=datetime.utcnow()
                )
                tech_user.set_password('tech123')  # Default password
                
                db.session.add(tech_user)
                db.session.commit()
                
                print("✅ Default technician user created successfully!")
                print("   Username: technician")
                print("   Password: tech123")
                print("   Email: <EMAIL>")
            else:
                print("ℹ️ Technician user already exists")
                print(f"   Username: {tech_user.username}")
                print(f"   Email: {tech_user.email}")
            
            # Create default customer user
            customer_user = User.query.filter_by(username='customer').first()
            
            if not customer_user:
                customer_user = User(
                    username='customer',
                    email='<EMAIL>',
                    full_name='Test Customer',
                    role='customer',
                    is_active=True,
                    created_at=datetime.utcnow()
                )
                customer_user.set_password('customer123')  # Default password
                
                db.session.add(customer_user)
                db.session.commit()
                
                print("✅ Default customer user created successfully!")
                print("   Username: customer")
                print("   Password: customer123")
                print("   Email: <EMAIL>")
            else:
                print("ℹ️ Customer user already exists")
                print(f"   Username: {customer_user.username}")
                print(f"   Email: {customer_user.email}")
            
            # Set default system settings
            try:
                SystemSettings.set_setting('company_name', 'Device Repair Center')
                SystemSettings.set_setting('company_address', '123 Main Street, City, Country')
                SystemSettings.set_setting('company_phone', '******-567-8900')
                SystemSettings.set_setting('company_email', '<EMAIL>')
                print("✅ Default system settings created")
            except Exception as e:
                print(f"⚠️ Could not set system settings: {e}")
            
            print("\n" + "="*50)
            print("🎉 System initialization completed successfully!")
            print("="*50)
            print("🌐 Access the system at: http://127.0.0.1:5000")
            print("🔧 Admin panel at: http://127.0.0.1:8081")
            print("="*50)
            
        except Exception as e:
            print(f"❌ Error creating default users: {e}")
            db.session.rollback()

if __name__ == '__main__':
    create_default_user()
