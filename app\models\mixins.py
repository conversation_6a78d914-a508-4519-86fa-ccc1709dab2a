from app.extensions import db
from app.middleware.tenant_context import get_current_tenant

class TenantAwareMixin:
    """Mixin to add tenant awareness to models"""

    tenant_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('tenant.id'), nullable=True, default=1)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set tenant_id from current context if not provided
        if not self.tenant_id:
            if hasattr(db.session, 'tenant_id'):
                self.tenant_id = db.session.tenant_id
            else:
                # Default to tenant_id = 1 for backward compatibility
                self.tenant_id = 1

    @classmethod
    def query_by_tenant(cls, tenant_id):
        """Query records for a specific tenant"""
        return cls.query.filter_by(tenant_id=tenant_id)

    @classmethod
    def get_by_id_and_tenant(cls, id, tenant_id):
        """Get a record by ID and tenant ID"""
        return cls.query.filter_by(id=id, tenant_id=tenant_id).first()

    @classmethod
    def get_tenant_query(cls):
        """Get query filtered by current tenant"""
        tenant = get_current_tenant()
        if not tenant:
            return cls.query.filter_by(id=None)  # Return empty query if no tenant
        return cls.query.filter_by(tenant_id=tenant.id)

    @classmethod
    def get_by_id(cls, id):
        """Get record by ID within tenant context"""
        return cls.get_tenant_query().filter_by(id=id).first()

    @classmethod
    def get_all(cls):
        """Get all records for current tenant"""
        return cls.get_tenant_query().all()

    def save(self):
        """Save record with tenant context"""
        if not self.tenant_id:
            tenant = get_current_tenant()
            if tenant:
                self.tenant_id = tenant.id
            else:
                # Default to tenant_id = 1 for backward compatibility
                self.tenant_id = 1
        db.session.add(self)
        db.session.commit()
        return self