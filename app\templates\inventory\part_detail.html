{% extends 'base.html' %}

{% block title %}{{ part.name }} - Part Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1><i class="fas fa-cog me-2"></i>{{ part.name }}</h1>
            <p class="text-muted">Part Number: {{ part.part_number }}</p>
        </div>
        <div>
            <a href="{{ url_for('inventory_enhanced.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Inventory
            </a>
            <a href="{{ url_for('inventory_enhanced.edit_part', part_id=part.id) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit Part
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Part Information -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>Part Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Name:</strong></td>
                            <td>{{ part.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Part Number:</strong></td>
                            <td>{{ part.part_number }}</td>
                        </tr>
                        <tr>
                            <td><strong>Category:</strong></td>
                            <td>{{ part.category or 'Uncategorized' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Brand:</strong></td>
                            <td>{{ part.brand or 'N/A' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Price:</strong></td>
                            <td class="text-success">${{ "%.2f"|format(part.price) }}</td>
                        </tr>
                        <tr>
                            <td><strong>Current Stock:</strong></td>
                            <td>
                                <span class="badge bg-{% if part.quantity == 0 %}danger{% elif part.quantity <= part.min_quantity %}warning{% else %}success{% endif %}">
                                    {{ part.quantity }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Minimum Stock:</strong></td>
                            <td>{{ part.min_quantity }}</td>
                        </tr>
                        <tr>
                            <td><strong>Location:</strong></td>
                            <td>{{ part.location or 'Not specified' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Supplier:</strong></td>
                            <td>{{ part.supplier or 'Not specified' }}</td>
                        </tr>
                    </table>
                    
                    {% if part.description %}
                    <div class="mt-3">
                        <strong>Description:</strong>
                        <p class="mt-2">{{ part.description }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Stock Management -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-warehouse me-2"></i>Stock Management</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('inventory_enhanced.update_stock', part_id=part.id) }}">
                        <div class="mb-3">
                            <label for="movement_type" class="form-label">Action</label>
                            <select class="form-select" name="movement_type" required>
                                <option value="add">Add Stock</option>
                                <option value="remove">Remove Stock</option>
                                <option value="set">Set Stock Level</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="quantity" class="form-label">Quantity</label>
                            <input type="number" class="form-control" name="quantity" min="1" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes (Optional)</label>
                            <textarea class="form-control" name="notes" rows="3"></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Stock
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-history me-2"></i>Recent Activity</h5>
                </div>
                <div class="card-body">
                    {% if movements %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Quantity</th>
                                    <th>Notes</th>
                                    <th>User</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for movement in movements %}
                                <tr>
                                    <td>{{ movement.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <span class="badge bg-{% if movement.movement_type == 'add' %}success{% elif movement.movement_type == 'remove' %}danger{% else %}info{% endif %}">
                                            {{ movement.movement_type.title() }}
                                        </span>
                                    </td>
                                    <td>{{ movement.quantity }}</td>
                                    <td>{{ movement.notes or '-' }}</td>
                                    <td>{{ movement.user.username if movement.user else 'System' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <p class="text-muted">No stock movements recorded yet.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
