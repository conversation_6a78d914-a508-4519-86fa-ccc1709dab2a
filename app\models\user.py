from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app.extensions import db, login_manager
from app.models.device import Device
from app.models.repair_ticket import RepairTicket
from app.models.settings import SystemSettings

# تعريف الأدوار المتاحة في النظام
ROLE_ADMIN = 'admin'  # مدير
ROLE_TECHNICIAN = 'technician'  # فني
ROLE_CUSTOMER = 'customer'  # عميل

class User(db.Model, UserMixin):
    id = db.Column(db.Integer, primary_key=True)

    # Multi-tenancy support
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenant.id'), nullable=True, index=True)

    username = db.Column(db.String(80), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    password_hash = db.Column(db.String(128))
    full_name = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    role = db.Column(db.String(20), default=ROLE_CUSTOMER)  # admin, technician, customer
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)
    is_active = db.Column(db.Boolean, default=True)

    # Unique constraints within tenant
    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'username', name='uq_user_tenant_username'),
        db.UniqueConstraint('tenant_id', 'email', name='uq_user_tenant_email'),
        db.Index('idx_user_tenant_role', 'tenant_id', 'role'),
    )
    # Additional fields for personal information
    address = db.Column(db.String(200), nullable=True)
    position = db.Column(db.String(50), nullable=True)  # Position for technicians
    department = db.Column(db.String(50), nullable=True)  # Department for technicians
    # Legacy fields that were kept
    email_verified = db.Column(db.Boolean, default=True)
    email_verification_sent_at = db.Column(db.DateTime, nullable=True)

    # Notification preferences
    telegram_chat_id = db.Column(db.String(50), nullable=True)
    notifications_enabled = db.Column(db.Boolean, default=True)
    notify_new_device = db.Column(db.Boolean, default=True)
    notify_status_change = db.Column(db.Boolean, default=True)
    notify_repair_complete = db.Column(db.Boolean, default=True)

    # Language preference for bilingual support
    language_preference = db.Column(db.String(5), default='en')  # 'en' or 'ar'

    # Trial license fields (for admin accounts)
    trial_start_date = db.Column(db.DateTime, nullable=True)
    trial_end_date = db.Column(db.DateTime, nullable=True)
    is_trial_account = db.Column(db.Boolean, default=False)
    trial_expired = db.Column(db.Boolean, default=False)
    license_type = db.Column(db.String(20), default='trial')  # trial, premium, enterprise

    # Relationships - specify foreign keys to avoid ambiguity
    # Note: devices relationship removed as customer_id points to Customer model
    assigned_devices = db.relationship('Device', backref='technician', lazy=True,
                                     foreign_keys='Device.technician_id')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        """Check if the user is an administrator"""
        return self.role == ROLE_ADMIN and self.is_active

    def is_technician(self):
        """Check if the user is a technician"""
        return self.role == ROLE_TECHNICIAN and self.is_active

    def is_customer(self):
        """Check if the user is a customer"""
        return self.role == ROLE_CUSTOMER and self.is_active

    def can_manage_users(self):
        """Check if the user can manage other users"""
        return self.is_admin()

    def can_manage_settings(self):
        """Check if the user can manage system settings"""
        return self.is_admin()

    def can_view_financial_reports(self):
        """Check if the user can view financial reports"""
        return self.is_admin()

    def can_add_devices(self):
        """Check if the user can add new devices"""
        return self.is_admin() or self.is_technician()

    def can_update_repair_status(self):
        """Check if the user can update repair status"""
        return self.is_admin() or self.is_technician()

    def can_estimate_repair_cost(self):
        """Check if the user can estimate repair costs"""
        return self.is_admin() or self.is_technician()

    def update_last_login(self):
        """Update the last login timestamp"""
        self.last_login = datetime.utcnow()

        # Initialize trial for admin accounts on first login
        if self.is_admin() and not self.trial_start_date:
            self.start_trial()

        db.session.commit()

    def start_trial(self):
        """Start the trial period for admin accounts"""
        if self.role == ROLE_ADMIN:
            from datetime import timedelta
            self.trial_start_date = datetime.utcnow()
            self.trial_end_date = self.trial_start_date + timedelta(days=7)
            self.is_trial_account = True
            self.trial_expired = False
            self.license_type = 'trial'

    def get_trial_days_remaining(self):
        """Get the number of days remaining in the trial"""
        if not self.is_trial_account or not self.trial_end_date:
            return None

        remaining = self.trial_end_date - datetime.utcnow()
        return max(0, remaining.days)

    def is_trial_active(self):
        """Check if the trial is still active"""
        if not self.is_trial_account:
            return False

        if not self.trial_end_date:
            return False

        return datetime.utcnow() < self.trial_end_date and not self.trial_expired

    def is_trial_expired(self):
        """Check if the trial has expired"""
        if not self.is_trial_account:
            return False

        if not self.trial_end_date:
            return True

        return datetime.utcnow() >= self.trial_end_date or self.trial_expired

    def expire_trial(self):
        """Manually expire the trial"""
        if self.is_trial_account:
            self.trial_expired = True
            self.is_active = False  # Disable account access

    def upgrade_from_trial(self, license_type='premium'):
        """Upgrade from trial to paid license"""
        self.is_trial_account = False
        self.trial_expired = False
        self.license_type = license_type
        self.is_active = True

    def can_perform_admin_actions(self):
        """Check if admin can perform actions (not in expired trial)"""
        if not self.is_admin():
            return False

        if self.is_trial_account:
            return self.is_trial_active()

        return True

    def get_trial_status_message(self):
        """Get a user-friendly trial status message"""
        if not self.is_trial_account:
            return None

        if self.is_trial_expired():
            return "Trial expired. Please upgrade to continue using admin features."

        days_remaining = self.get_trial_days_remaining()
        if days_remaining == 0:
            return "Trial expires today! Please upgrade to continue."
        elif days_remaining <= 3:
            return f"Trial expires in {days_remaining} day{'s' if days_remaining != 1 else ''}. Consider upgrading soon."
        else:
            return f"Trial active - {days_remaining} days remaining."

    def __repr__(self):
        return f'<User {self.username}>'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))