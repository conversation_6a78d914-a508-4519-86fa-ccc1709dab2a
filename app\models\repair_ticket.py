from datetime import datetime
from app.extensions import db
from app.models.mixins import TenantAwareMixin
from app.models.device import UsedPart

class RepairTicket(db.Model, TenantAwareMixin):
    """Repair ticket model for tracking device repairs"""
    __tablename__ = 'repair_ticket'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.Integer, db.ForeignKey('device.id'), nullable=False)
    issue_description = db.Column(db.Text, nullable=False)
    diagnosis = db.Column(db.Text)
    repair_notes = db.Column(db.Text)
    status = db.Column(db.String(20), default='received')  # received, in_progress, repaired, delivered
    priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
    cost = db.Column(db.Float, default=0.0)
    payment_status = db.Column(db.String(20), default='pending')  # pending, paid, partial
    warranty_period = db.Column(db.Integer, default=90)  # warranty period in days
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    device = db.relationship('Device', back_populates='repair_tickets', lazy=True)
    used_parts = db.relationship('UsedPart', backref='repair_ticket', lazy=True)
    
    def __repr__(self):
        return f'<RepairTicket {self.id}>'
    
    @property
    def total_parts_cost(self):
        return sum(part.part.price * part.quantity for part in self.used_parts)

    @property
    def total_cost(self):
        return self.cost + self.total_parts_cost

    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'device_id': self.device_id,
            'issue_description': self.issue_description,
            'diagnosis': self.diagnosis,
            'repair_notes': self.repair_notes,
            'status': self.status,
            'priority': self.priority,
            'cost': self.cost,
            'payment_status': self.payment_status,
            'warranty_period': self.warranty_period,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        } 