{% extends "simple_base.html" %}

{% block title %}لوحة التحكم - نظام إدارة إصلاح الأجهزة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-speedometer2"></i> لوحة التحكم
    </h2>
    <div class="text-muted">
        مرحباً، {{ current_user.full_name or current_user.username }}
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_devices }}</h4>
                        <p class="mb-0">إجمالي الأجهزة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-phone display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ received_count }}</h4>
                        <p class="mb-0">مستلمة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-inbox display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ in_repair_count }}</h4>
                        <p class="mb-0">قيد الإصلاح</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-tools display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ repaired_count }}</h4>
                        <p class="mb-0">مُصلحة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Devices -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history"></i> الأجهزة الحديثة
                </h5>
                <a href="{{ url_for('devices') }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_devices %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>النوع</th>
                                <th>الموديل</th>
                                <th>المالك</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for device in recent_devices %}
                            <tr>
                                <td>{{ device.type }}</td>
                                <td>{{ device.model }}</td>
                                <td>{{ device.owner_name }}</td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if device.status == 'received' else 'warning' if device.status == 'in_repair' else 'success' if device.status == 'repaired' else 'secondary' }}">
                                        {% if device.status == 'received' %}مستلم
                                        {% elif device.status == 'in_repair' %}قيد الإصلاح
                                        {% elif device.status == 'repaired' %}مُصلح
                                        {% elif device.status == 'delivered' %}مُسلم
                                        {% else %}{{ device.status }}{% endif %}
                                    </span>
                                </td>
                                <td>{{ device.created_at.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="bi bi-inbox display-4"></i>
                    <p class="mt-2">لا توجد أجهزة حتى الآن</p>
                    <a href="{{ url_for('add_device') }}" class="btn btn-primary">
                        <i class="bi bi-plus"></i> إضافة جهاز جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Low Stock Alert -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-exclamation-triangle text-warning"></i> تنبيهات المخزون
                </h5>
                <a href="{{ url_for('spare_parts') }}" class="btn btn-sm btn-outline-warning">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if low_stock_items %}
                <div class="list-group list-group-flush">
                    {% for item in low_stock_items[:5] %}
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <h6 class="mb-1">{{ item.name }}</h6>
                            <small class="text-muted">{{ item.part_number }}</small>
                        </div>
                        <span class="badge bg-{{ 'danger' if item.quantity == 0 else 'warning' }} rounded-pill">
                            {{ item.quantity }}
                        </span>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="bi bi-check-circle display-4 text-success"></i>
                    <p class="mt-2">جميع قطع الغيار متوفرة</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('add_device') }}" class="btn btn-primary">
                        <i class="bi bi-plus"></i> إضافة جهاز جديد
                    </a>
                    <a href="{{ url_for('add_spare_part') }}" class="btn btn-success">
                        <i class="bi bi-gear"></i> إضافة قطعة غيار
                    </a>
                    <a href="{{ url_for('devices') }}" class="btn btn-info">
                        <i class="bi bi-search"></i> البحث في الأجهزة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
