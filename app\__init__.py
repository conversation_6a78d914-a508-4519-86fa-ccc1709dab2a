import os
from flask import Flask, jsonify, redirect, url_for, g, request, session
from dotenv import load_dotenv
from datetime import datetime, timedelta
from config import Config
from app.extensions import db, migrate, login_manager, csrf
from app.models.device import Device
from app.models.repair_ticket import RepairTicket
from app.models.settings import SystemSettings

# Load environment variables
load_dotenv()

def create_app(config_class=Config):
    app = Flask(__name__)

    # Create an instance of the config class
    if isinstance(config_class, type):
        config_instance = config_class()
    else:
        config_instance = config_class

    app.config.from_object(config_instance)

    # Initialize extensions with app
    db.init_app(app)

    # Initialize migrate if available
    if migrate:
        migrate.init_app(app, db)

    login_manager.init_app(app)
    login_manager.login_view = 'main.index'  # Fallback to main page instead of auth.login
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'

    # Initialize CSRF protection if available
    if csrf:
        csrf.init_app(app)
        app.csrf = csrf
    else:
        app.csrf = None

    # English-only configuration
    app.config['LANGUAGES'] = {'en': 'English'}
    app.config['BABEL_DEFAULT_LOCALE'] = 'en'
    app.config['BABEL_DEFAULT_TIMEZONE'] = 'UTC'

    # Template context processors for English-only support
    @app.context_processor
    def inject_template_vars():
        # Provide simple English-only functions
        def get_locale_fallback():
            return 'en'  # Always English

        def gettext_fallback(text):
            return text  # Return text as-is

        def ngettext_fallback(singular, plural, num):
            return singular if num == 1 else plural

        return {
            'get_locale': get_locale_fallback,
            '_': gettext_fallback,
            'ngettext': ngettext_fallback,
            'current_language': 'en',
            'is_rtl': False,
            'available_languages': app.config['LANGUAGES']
        }

    # Initialize enhanced services (SocketIO removed for simplified setup)
    try:
        from app.utils.error_handler import ErrorHandler
        error_handler = ErrorHandler(app)
        app.error_handler = error_handler
    except ImportError:
        print("Warning: Error handler not available")

    try:
        from app.services.unified_notification_service_v2 import unified_notification_service
        app.notification_service = unified_notification_service
    except ImportError:
        print("Warning: Notification service not available")

    app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)
    app.config['SESSION_COOKIE_SECURE'] = False  # Allow HTTP for development
    app.config['SESSION_COOKIE_HTTPONLY'] = True
    app.config['WTF_CSRF_TIME_LIMIT'] = None  # No time limit for CSRF tokens

    # Root route removed - handled by main blueprint

    # Server status endpoint
    @app.route('/status')
    def server_status():
        return jsonify({
            'status': 'online',
            'server_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

    # Public system status endpoint for admin panel integration
    @app.route('/api/system-status')
    def public_system_status():
        """Public system status endpoint for admin panel integration"""
        try:
            from app.models.user import User
            from app.models.device import Device
            from app.models.subscription import SubscriptionPlan
            from app.models.tenant import Tenant
            from app.utils.system_info import get_machine_id
            # Get basic counts
            user_count = User.query.count()
            device_count = Device.query.count()
            plan_count = SubscriptionPlan.query.count()
            tenant_count = Tenant.query.count()

            # Get machine ID (activation system removed)
            machine_id = get_machine_id()
            is_activated = True  # System no longer requires activation
            activation_info = {'is_active': True, 'status': 'System activation removed'}

            return jsonify({
                'status': 'online',
                'server_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'main_app_running': True,
                'machine_id': machine_id,
                'activation_status': is_activated,
                'activation_info': activation_info,
                'statistics': {
                    'users': user_count,
                    'devices': device_count,
                    'plans': plan_count,
                    'tenants': tenant_count
                }
            })
        except Exception as e:
            # Fallback response with basic machine ID
            try:
                from app.utils.system_info import get_machine_id
                machine_id = get_machine_id()
            except:
                machine_id = "unknown"

            return jsonify({
                'status': 'online',
                'server_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'main_app_running': True,
                'machine_id': machine_id,
                'activation_status': False,
                'error': str(e)
            })

    # Register blueprints (ADMIN BLUEPRINT REMOVED FOR COMPLETE SEPARATION)
    # Core blueprints (required)
    try:
        from app.routes.main import main_bp
        app.register_blueprint(main_bp)
    except ImportError as e:
        print(f"Warning: Main routes not available: {e}")

    try:
        from app.routes.auth import auth_bp
        app.register_blueprint(auth_bp, url_prefix='/auth')
    except ImportError as e:
        print(f"Warning: Auth routes not available: {e}")

    try:
        from app.routes.device import device_bp
        app.register_blueprint(device_bp, url_prefix='/devices')
    except ImportError as e:
        print(f"Warning: Device routes not available: {e}")

    # Enhanced features (required for our improvements)
    try:
        from app.routes.inventory_enhanced import inventory_enhanced_bp
        app.register_blueprint(inventory_enhanced_bp, url_prefix='/inventory')
        print("✅ Enhanced inventory routes registered")
    except ImportError as e:
        print(f"Warning: Enhanced inventory routes not available: {e}")

    try:
        from app.routes.accounting import accounting_bp
        app.register_blueprint(accounting_bp, url_prefix='/accounting')
        print("✅ Enhanced accounting routes registered")
    except ImportError as e:
        print(f"Warning: Accounting routes not available: {e}")

    # Optional blueprints (graceful fallback)
    optional_blueprints = [
        ('app.routes.technician', 'technician_bp', '/technician'),
        ('app.routes.dashboard', 'dashboard_bp', '/dashboard'),
        ('app.routes.users', 'users_bp', '/users'),
        ('app.routes.staff', 'staff_bp', '/staff'),
        ('app.routes.discount', 'discount_bp', '/discounts'),
        ('app.routes.billing', 'billing_bp', '/billing'),
        ('app.routes.spare_parts', 'spare_parts_bp', '/spare-parts'),
    ]

    for module_name, blueprint_name, url_prefix in optional_blueprints:
        try:
            module = __import__(module_name, fromlist=[blueprint_name])
            blueprint = getattr(module, blueprint_name)
            app.register_blueprint(blueprint, url_prefix=url_prefix)
        except (ImportError, AttributeError) as e:
            print(f"Warning: {module_name} not available: {e}")

    # API blueprints (optional)
    try:
        from app.api.activation_sync import activation_sync_bp
        app.register_blueprint(activation_sync_bp)
    except ImportError as e:
        print(f"Warning: Activation sync API not available: {e}")

    try:
        from app.api.device_api import device_api_bp
        app.register_blueprint(device_api_bp)
    except ImportError as e:
        print(f"Warning: Device API not available: {e}")

    # Blueprint registration completed above with try-catch blocks

    # Exempt AJAX status update route from CSRF protection
    if csrf:
        with app.app_context():
            try:
                from app.routes.device import update_status_ajax
                csrf.exempt(update_status_ajax)
            except:
                pass

        # CSRF exemptions for optional blueprints handled in their respective modules

    # Exempt specific API endpoints from CSRF protection
    @app.route('/api/activate', methods=['POST'])
    def public_api_activate():
        """Secure API endpoint for activation code processing (CSRF exempt)"""
        from flask import request, jsonify

        data = request.get_json()

        if not data or 'code' not in data:
            return jsonify({'success': False, 'message': 'Activation code is required'}), 400

        code = data['code'].strip().upper()

        # Enhanced security validation
        if len(code) != 16:
            return jsonify({'success': False, 'message': 'Invalid activation code format. Codes must be exactly 16 characters.'}), 400

        # Additional format validation
        import re
        if not re.match(r'^[A-Z0-9]{16}$', code):
            return jsonify({'success': False, 'message': 'Invalid activation code format. Only uppercase letters and numbers allowed.'}), 400

        # Import activation service
        from app.services.activation_service import ActivationService

        try:
            result = ActivationService.activate_code(code)

            if result['success']:
                return jsonify(result)
            else:
                return jsonify(result), 400

        except Exception as e:
            return jsonify({'success': False, 'message': 'Activation failed due to security validation'}), 500

    # Exempt admin API endpoints from CSRF protection
    @app.route('/api/admin/generate-codes', methods=['POST'])
    def public_api_admin_generate_codes():
        """Public API endpoint for generating activation codes (CSRF exempt)"""
        from flask import request, jsonify
        import sqlite3
        import secrets
        from datetime import datetime, timedelta

        data = request.get_json()
        count = data.get('count', 1)
        duration = data.get('duration', 365)
        description = data.get('description', 'Generated from API')

        try:
            codes = []
            conn = sqlite3.connect('instance/app_fixed.db')
            cursor = conn.cursor()

            for i in range(count):
                # Generate unique code
                code = ''.join(secrets.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') for _ in range(16))

                # Ensure uniqueness
                while True:
                    cursor.execute("SELECT id FROM activation WHERE code = ?", (code,))
                    if not cursor.fetchone():
                        break
                    code = ''.join(secrets.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') for _ in range(16))

                # Create activation record
                expiry_date = datetime.now() + timedelta(days=duration)

                cursor.execute("""
                    INSERT INTO activation
                    (code, client_id, activation_date, expiry_date, is_active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    code,
                    'PENDING',
                    datetime.now(),
                    expiry_date,
                    0,
                    datetime.now(),
                    datetime.now()
                ))

                codes.append({
                    'code': code,
                    'duration': duration,
                    'expiry_date': expiry_date.isoformat()
                })

            conn.commit()
            conn.close()

            return jsonify({
                'success': True,
                'codes': codes,
                'count': len(codes),
                'duration': duration,
                'description': description,
                'message': f'Successfully generated {len(codes)} activation codes!'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'Error generating codes: {str(e)}'
            }), 500

    # Initialize CLI commands
    from app.cli import init_app as init_cli
    init_cli(app)

    # Activation guard middleware removed - system no longer requires activation

    # Register tenant middleware (simplified for now)
    try:
        from app.middleware.tenant_context import set_tenant_context, clear_tenant_context, get_tenant_from_request

        @app.before_request
        def before_request():
            """Set tenant context before each request"""
            try:
                tenant = get_tenant_from_request()
                if tenant:
                    set_tenant_context(tenant)
                else:
                    clear_tenant_context()
            except Exception as e:
                # Log the error but don't break the request
                print(f"Warning: Could not get tenant from request: {e}")
                clear_tenant_context()

        @app.after_request
        def after_request(response):
            """Clear tenant context after each request"""
            try:
                clear_tenant_context()
            except:
                pass
            return response
    except ImportError as e:
        print(f"Warning: Tenant middleware not available: {e}")
        # Continue without tenant middleware

    # Create database tables and initialize data
    with app.app_context():
        # Create tables first
        try:
            db.create_all()
            print("✅ Database tables created successfully")
        except Exception as e:
            print(f"Database creation error: {e}")

        # Run migrations if available
        try:
            from app.utils.migrate import run_migrations
            run_migrations()
        except ImportError:
            print("Migration utilities not found, skipping...")
        except Exception as e:
            print(f"Migration error: {e}")

        # Run device status migration if available
        try:
            from app.utils.migrate_device_status import migrate_device_status
            migrate_device_status()
        except ImportError:
            print("Device status migration not found, skipping...")
        except Exception as e:
            print(f"Device status migration error: {e}")

        # Initialize default data
        try:
            from app.utils.init_db import init_db
            init_db()
        except Exception as e:
            print(f"Database initialization error: {e}")

    # Ensure SECRET_KEY is set
    if not app.config.get('SECRET_KEY'):
        app.config['SECRET_KEY'] = os.getenv('SECRET_KEY') or 'dev-secret-key-2025'
        print(f"✅ SECRET_KEY configured")

    return app