from flask import Blueprint, render_template, send_from_directory, current_app
import os

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Main landing page for SaaS system"""
    from app.models.tenant_registration import TenantSubscriptionPlan

    try:
        # Get subscription plans for display
        plans = TenantSubscriptionPlan.get_active_plans()
    except:
        # If no plans available, create default ones
        plans = []

    return render_template('landing.html', plans=plans)

@main_bp.route('/dashboard')
def dashboard():
    """Redirect to main dashboard for existing users"""
    from flask import redirect, url_for
    return redirect(url_for('dashboard.index'))

@main_bp.route('/about')
def about():
    return render_template('about.html')

@main_bp.route('/contact')
def contact():
    return render_template('contact.html')

# Activation routes (temporarily disabled but kept for compatibility)
@main_bp.route('/activate')
def activate_form():
    """Show activation form (system is currently open access)"""
    return render_template('activate.html')

@main_bp.route('/activate', methods=['POST'])
def activate_code():
    """Process activation code (currently bypassed)"""
    from flask import redirect, url_for, flash
    flash('System is currently running in open access mode. No activation required.', 'info')
    return redirect(url_for('dashboard.index'))

# Admin routes removed - now handled by separate standalone admin tool

@main_bp.route('/favicon.ico')
def favicon():
    """Serve favicon.ico"""
    return send_from_directory(os.path.join(current_app.root_path, 'static'), 'favicon.ico')

