from flask import Blueprint, render_template, send_from_directory, current_app
import os

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Main landing page for SaaS system"""
    from app.models.tenant_registration import TenantSubscriptionPlan

    try:
        # Get subscription plans for display
        plans = TenantSubscriptionPlan.get_active_plans()
    except:
        # If no plans available, create default ones
        plans = []

    return render_template('landing.html', plans=plans)

@main_bp.route('/dashboard')
def dashboard():
    """Redirect to main dashboard for existing users"""
    from flask import redirect, url_for
    return redirect(url_for('dashboard.index'))

@main_bp.route('/about')
def about():
    return render_template('about.html')

@main_bp.route('/contact')
def contact():
    return render_template('contact.html')

# Activation routes (temporarily disabled but kept for compatibility)
@main_bp.route('/activate')
def activate_form():
    """Show activation form (system is currently open access)"""
    return render_template('activate.html')

@main_bp.route('/activate', methods=['POST'])
def activate_code():
    """Process activation code (currently bypassed)"""
    from flask import redirect, url_for, flash
    flash('System is currently running in open access mode. No activation required.', 'info')
    return redirect(url_for('dashboard.index'))

# Admin routes removed - now handled by separate standalone admin tool

@main_bp.route('/favicon.ico')
def favicon():
    """Serve favicon.ico"""
    return send_from_directory(os.path.join(current_app.root_path, 'static'), 'favicon.ico')

@main_bp.route('/system/info')
def system_info():
    """System information page for desktop application"""
    from flask import render_template
    import platform
    import os
    from datetime import datetime

    try:
        return render_template('system_info.html')
    except:
        # Fallback if template doesn't exist
        try:
            # Get system information (without psutil dependency)
            system_info = {
                'os': platform.system(),
                'os_version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor() or 'Unknown',
                'python_version': platform.python_version(),
                'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        except:
            system_info = {
                'os': 'Windows',
                'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        return f'''
        <!DOCTYPE html>
        <html>
        <head>
            <title>معلومات النظام - Device Repair Management</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        </head>
        <body>
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h4><i class="fas fa-info-circle me-2"></i>معلومات النظام</h4>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5><i class="fas fa-desktop me-2"></i>معلومات التطبيق</h5>
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item"><strong>اسم التطبيق:</strong> Device Repair Management System</li>
                                            <li class="list-group-item"><strong>الإصدار:</strong> 2.0</li>
                                            <li class="list-group-item"><strong>نوع التطبيق:</strong> Desktop Application</li>
                                            <li class="list-group-item"><strong>قاعدة البيانات:</strong> SQLite</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h5><i class="fas fa-cog me-2"></i>معلومات النظام</h5>
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item"><strong>نظام التشغيل:</strong> {system_info.get('os', 'N/A')}</li>
                                            <li class="list-group-item"><strong>إصدار Python:</strong> {system_info.get('python_version', 'N/A')}</li>
                                            <li class="list-group-item"><strong>الوقت الحالي:</strong> {system_info.get('current_time', 'N/A')}</li>
                                            <li class="list-group-item"><strong>حالة الخادم:</strong> <span class="badge bg-success">متصل</span></li>
                                        </ul>
                                    </div>
                                </div>
                                <hr>
                                <div class="text-center">
                                    <a href="/" class="btn btn-primary me-2"><i class="fas fa-home me-1"></i>العودة للرئيسية</a>
                                    <a href="/admin" class="btn btn-secondary"><i class="fas fa-tools me-1"></i>لوحة الإدارة</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        '''

# Redirect old tenant registration to system info
@main_bp.route('/tenant/register')
def tenant_register_redirect():
    """Redirect old tenant registration to system info"""
    from flask import redirect, url_for
    return redirect(url_for('main.system_info'))

@main_bp.route('/support/chat')
def support_chat():
    """Support chat page (simplified)"""
    try:
        return render_template('support_chat_simple.html')
    except:
        # Fallback if template doesn't exist
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Support Chat</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        </head>
        <body>
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h4>Support Chat</h4>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h5>Support Chat Coming Soon</h5>
                                    <p>Our live chat support system is currently being set up.</p>
                                    <p>For immediate assistance, please contact us via:</p>
                                    <ul>
                                        <li>Email: <EMAIL></li>
                                        <li>Phone: ******-567-8900</li>
                                    </ul>
                                </div>
                                <a href="/" class="btn btn-primary">Return to Home</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        '''

