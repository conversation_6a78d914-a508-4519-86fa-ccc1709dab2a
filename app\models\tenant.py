from datetime import datetime
from app.extensions import db

class Tenant(db.Model):
    """Tenant model for multi-tenancy support"""
    __tablename__ = 'tenant'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    subdomain = db.Column(db.String(50), unique=True, nullable=False)
    is_active = db.Column(db.<PERSON>, default=True)
    
    # Contact information
    email = db.Column(db.String(120), unique=True, nullable=False)
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    
    # Settings
    settings = db.Column(db.JSON, default=dict)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships - using string references to avoid circular imports
    users = db.relationship('User', backref=db.backref('tenant', lazy=True))
    devices = db.relationship('Device', backref=db.backref('tenant', lazy=True))
    subscriptions = db.relationship('TenantSubscription', backref=db.backref('tenant', lazy=True))
    
    def __repr__(self):
        return f'<Tenant {self.name}>'
    
    @property
    def current_subscription(self):
        """Get the current active subscription"""
        from app.models.subscription import TenantSubscription
        return TenantSubscription.query.filter_by(
            tenant_id=self.id,
            status='active'
        ).order_by(TenantSubscription.created_at.desc()).first()
    
    @property
    def subscription_plan(self):
        """Get the current subscription plan"""
        subscription = self.current_subscription
        return subscription.plan if subscription else None
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'name': self.name,
            'subdomain': self.subdomain,
            'is_active': self.is_active,
            'email': self.email,
            'phone': self.phone,
            'address': self.address,
            'settings': self.settings,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'subscription': self.current_subscription.to_dict() if self.current_subscription else None
        }
