"""
Comprehensive Error Handling and Logging System
Provides centralized error handling, logging, and monitoring
"""

import logging
import traceback
import sys
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from functools import wraps
from flask import request, jsonify, render_template, current_app, session
from werkzeug.exceptions import HTTPException
import sqlite3
import os


class ErrorHandler:
    """Centralized error handling and logging system"""
    
    def __init__(self, app=None):
        self.app = app
        self.logger = None
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize error handler with Flask app"""
        self.app = app
        self.setup_logging()
        self.register_error_handlers()
        
    def setup_logging(self):
        """Setup comprehensive logging configuration"""
        # Create logs directory
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # Configure main logger
        self.logger = logging.getLogger('device_repair_system')
        self.logger.setLevel(logging.INFO)
        
        # Remove existing handlers to avoid duplicates
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # File handler for general logs
        file_handler = logging.FileHandler(
            os.path.join(log_dir, 'application.log'),
            encoding='utf-8'
        )
        file_handler.setLevel(logging.INFO)
        
        # File handler for errors
        error_handler = logging.FileHandler(
            os.path.join(log_dir, 'errors.log'),
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        
        # Console handler for development
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        file_handler.setFormatter(formatter)
        error_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
        self.logger.addHandler(console_handler)
        
        # Prevent propagation to root logger
        self.logger.propagate = False
        
        self.logger.info("Error handling system initialized")
    
    def register_error_handlers(self):
        """Register error handlers with Flask app"""
        
        @self.app.errorhandler(404)
        def handle_404(error):
            return self.handle_error(error, 404)
        
        @self.app.errorhandler(500)
        def handle_500(error):
            return self.handle_error(error, 500)
        
        @self.app.errorhandler(403)
        def handle_403(error):
            return self.handle_error(error, 403)
        
        @self.app.errorhandler(Exception)
        def handle_exception(error):
            # Handle non-HTTP exceptions
            if isinstance(error, HTTPException):
                return self.handle_error(error, error.code)
            else:
                return self.handle_error(error, 500)
    
    def handle_error(self, error, status_code: int):
        """Handle errors with appropriate response format"""
        try:
            # Log the error
            self.log_error(error, status_code)
            
            # Determine response format
            if request.is_json or request.path.startswith('/api/'):
                return self.json_error_response(error, status_code)
            else:
                return self.html_error_response(error, status_code)
                
        except Exception as e:
            # Fallback error handling
            self.logger.error(f"Error in error handler: {e}")
            return self.fallback_error_response(status_code)
    
    def log_error(self, error, status_code: int):
        """Log error with comprehensive information"""
        try:
            # Safely get request information
            try:
                url = getattr(request, 'url', 'N/A') if request else 'N/A'
                method = getattr(request, 'method', 'N/A') if request else 'N/A'
                user_agent = 'N/A'
                ip_address = 'N/A'
                user_id = 'Anonymous'

                if request:
                    try:
                        user_agent = request.headers.get('User-Agent', 'N/A')
                    except:
                        pass
                    try:
                        ip_address = request.remote_addr or 'N/A'
                    except:
                        pass
                    try:
                        if hasattr(request, 'session') and session:
                            user_id = session.get('user_id', 'Anonymous')
                    except:
                        pass
            except Exception:
                url = 'N/A'
                method = 'N/A'
                user_agent = 'N/A'
                ip_address = 'N/A'
                user_id = 'Anonymous'

            error_info = {
                'timestamp': datetime.now().isoformat(),
                'status_code': status_code,
                'error_type': type(error).__name__,
                'error_message': str(error),
                'url': url,
                'method': method,
                'user_agent': user_agent,
                'ip_address': ip_address,
                'user_id': user_id
            }
            
            # Add traceback for 500 errors
            if status_code == 500:
                error_info['traceback'] = traceback.format_exc()
            
            # Log based on severity (with filtering for common non-critical warnings)
            if status_code >= 500:
                self.logger.error(f"Server Error: {error_info}")
            elif status_code >= 400:
                # Filter out common non-critical warnings
                if not self._should_ignore_warning(error_info):
                    self.logger.warning(f"Client Error: {error_info}")
            else:
                self.logger.info(f"Request Info: {error_info}")
            
            # Store in database for monitoring
            self.store_error_in_db(error_info)
            
        except Exception as e:
            # Fallback logging
            print(f"Error logging failed: {e}")

    def _should_ignore_warning(self, error_info: Dict[str, Any]) -> bool:
        """Check if warning should be ignored (common non-critical warnings)"""
        try:
            error_message = error_info.get('error_message', '').lower()
            url = error_info.get('url', '').lower()

            # Ignore common non-critical warnings
            ignore_patterns = [
                'invalid host format',
                'host header',
                'favicon.ico',
                'robots.txt',
                'sitemap.xml'
            ]

            for pattern in ignore_patterns:
                if pattern in error_message or pattern in url:
                    return True

            return False
        except:
            return False

    def store_error_in_db(self, error_info: Dict[str, Any]):
        """Store error information in database for monitoring"""
        try:
            conn = sqlite3.connect('instance/app.db')
            cursor = conn.cursor()
            
            # Create error_logs table if it doesn't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS error_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    status_code INTEGER NOT NULL,
                    error_type TEXT NOT NULL,
                    error_message TEXT NOT NULL,
                    url TEXT,
                    method TEXT,
                    user_agent TEXT,
                    ip_address TEXT,
                    user_id TEXT,
                    traceback TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Insert error log
            cursor.execute("""
                INSERT INTO error_logs 
                (timestamp, status_code, error_type, error_message, url, method, 
                 user_agent, ip_address, user_id, traceback)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                error_info['timestamp'],
                error_info['status_code'],
                error_info['error_type'],
                error_info['error_message'],
                error_info['url'],
                error_info['method'],
                error_info['user_agent'],
                error_info['ip_address'],
                error_info['user_id'],
                error_info.get('traceback')
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Failed to store error in database: {e}")
    
    def json_error_response(self, error, status_code: int):
        """Return JSON error response for API requests"""
        response_data = {
            'success': False,
            'error': {
                'code': status_code,
                'type': type(error).__name__,
                'message': self.get_user_friendly_message(status_code),
                'timestamp': datetime.now().isoformat()
            }
        }
        
        # Add debug info in development
        if current_app.debug and status_code == 500:
            response_data['error']['debug'] = str(error)
        
        return jsonify(response_data), status_code
    
    def html_error_response(self, error, status_code: int):
        """Return HTML error response for web requests"""
        try:
            return render_template(
                'error.html',
                error_code=status_code,
                error_message=self.get_user_friendly_message(status_code),
                debug_info=str(error) if current_app.debug else None
            ), status_code
        except:
            # Fallback if template rendering fails
            return self.fallback_error_response(status_code)
    
    def fallback_error_response(self, status_code: int):
        """Fallback error response when all else fails"""
        message = self.get_user_friendly_message(status_code)
        
        if request and (request.is_json or request.path.startswith('/api/')):
            return jsonify({
                'success': False,
                'error': {
                    'code': status_code,
                    'message': message
                }
            }), status_code
        else:
            return f"""
            <!DOCTYPE html>
            <html>
            <head><title>Error {status_code}</title></head>
            <body>
                <h1>Error {status_code}</h1>
                <p>{message}</p>
                <a href="/">Return to Home</a>
            </body>
            </html>
            """, status_code
    
    def get_user_friendly_message(self, status_code: int) -> str:
        """Get user-friendly error messages"""
        messages = {
            400: "Bad request. Please check your input and try again.",
            401: "Authentication required. Please log in.",
            403: "Access denied. You don't have permission to access this resource.",
            404: "Page not found. The requested resource could not be found.",
            405: "Method not allowed. Please use a different request method.",
            429: "Too many requests. Please wait a moment and try again.",
            500: "Internal server error. We're working to fix this issue.",
            502: "Bad gateway. The server is temporarily unavailable.",
            503: "Service unavailable. Please try again later."
        }
        
        return messages.get(status_code, f"An error occurred (Code: {status_code})")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics for monitoring"""
        try:
            conn = sqlite3.connect('instance/app.db')
            cursor = conn.cursor()
            
            # Get error counts by status code
            cursor.execute("""
                SELECT status_code, COUNT(*) as count
                FROM error_logs
                WHERE created_at > datetime('now', '-24 hours')
                GROUP BY status_code
                ORDER BY count DESC
            """)
            
            error_counts = dict(cursor.fetchall())
            
            # Get recent errors
            cursor.execute("""
                SELECT timestamp, status_code, error_type, error_message, url
                FROM error_logs
                ORDER BY created_at DESC
                LIMIT 10
            """)
            
            recent_errors = [
                {
                    'timestamp': row[0],
                    'status_code': row[1],
                    'error_type': row[2],
                    'error_message': row[3],
                    'url': row[4]
                }
                for row in cursor.fetchall()
            ]
            
            conn.close()
            
            return {
                'error_counts_24h': error_counts,
                'recent_errors': recent_errors,
                'total_errors_24h': sum(error_counts.values())
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get error statistics: {e}")
            return {'error': 'Failed to retrieve statistics'}


def handle_exceptions(f):
    """Decorator for handling exceptions in route functions"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            # Log the error
            logger = logging.getLogger('device_repair_system')
            logger.error(f"Exception in {f.__name__}: {e}", exc_info=True)
            
            # Return appropriate error response
            if request.is_json or request.path.startswith('/api/'):
                return jsonify({
                    'success': False,
                    'error': 'An internal error occurred'
                }), 500
            else:
                return render_template('error.html', 
                                     error_code=500,
                                     error_message="An internal error occurred"), 500
    
    return decorated_function


# Global error handler instance
error_handler = ErrorHandler()
