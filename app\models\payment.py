from datetime import datetime
from app import db
from app.models.mixins import TenantAwareMixin

class Payment(db.Model, TenantAwareMixin):
    """Payment model for tracking subscription payments"""
    __tablename__ = 'payment'
    
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('tenant_subscription.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='USD')
    status = db.Column(db.String(20), default='pending')  # pending, completed, failed, refunded
    payment_method = db.Column(db.String(50))  # stripe, bank_transfer, etc.
    payment_provider_id = db.Column(db.String(100))  # External payment provider ID
    payment_provider_data = db.Column(db.Text)  # JSON data from payment provider
    invoice_number = db.Column(db.String(50))
    invoice_url = db.Column(db.String(255))
    paid_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    subscription = db.relationship('TenantSubscription', backref='payments')
    
    def __repr__(self):
        return f'<Payment {self.id} - {self.amount} {self.currency}>'
    
    def mark_as_paid(self, payment_provider_id=None, payment_provider_data=None):
        """Mark payment as completed"""
        self.status = 'completed'
        self.paid_at = datetime.utcnow()
        if payment_provider_id:
            self.payment_provider_id = payment_provider_id
        if payment_provider_data:
            self.payment_provider_data = payment_provider_data
        self.save()
    
    def mark_as_failed(self, payment_provider_data=None):
        """Mark payment as failed"""
        self.status = 'failed'
        if payment_provider_data:
            self.payment_provider_data = payment_provider_data
        self.save()
    
    def refund(self, payment_provider_data=None):
        """Mark payment as refunded"""
        self.status = 'refunded'
        if payment_provider_data:
            self.payment_provider_data = payment_provider_data
        self.save()
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'subscription_id': self.subscription_id,
            'amount': self.amount,
            'currency': self.currency,
            'status': self.status,
            'payment_method': self.payment_method,
            'invoice_number': self.invoice_number,
            'invoice_url': self.invoice_url,
            'paid_at': self.paid_at.isoformat() if self.paid_at else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class PaymentInvoice(db.Model, TenantAwareMixin):
    """Invoice model for generating and tracking invoices"""
    __tablename__ = 'payment_invoice'
    
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('tenant_subscription.id'), nullable=False)
    invoice_number = db.Column(db.String(50), unique=True)
    amount = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), default='USD')
    status = db.Column(db.String(20), default='draft')  # draft, sent, paid, void
    due_date = db.Column(db.DateTime)
    paid_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Billing details
    billing_name = db.Column(db.String(100))
    billing_email = db.Column(db.String(120))
    billing_address = db.Column(db.Text)
    billing_tax_id = db.Column(db.String(50))
    
    # Invoice items stored as JSON
    items = db.Column(db.Text)  # JSON array of invoice items
    
    # Relationships
    subscription = db.relationship('TenantSubscription', backref='payment_invoices')
    # Note: Payment relationship is handled via invoice_number field, not foreign key
    
    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'
    
    def mark_as_paid(self, paid_at=None):
        """Mark invoice as paid"""
        self.status = 'paid'
        self.paid_at = paid_at or datetime.utcnow()
        self.save()
    
    def mark_as_void(self):
        """Mark invoice as void"""
        self.status = 'void'
        self.save()
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'subscription_id': self.subscription_id,
            'invoice_number': self.invoice_number,
            'amount': self.amount,
            'currency': self.currency,
            'status': self.status,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'paid_at': self.paid_at.isoformat() if self.paid_at else None,
            'billing_name': self.billing_name,
            'billing_email': self.billing_email,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        } 