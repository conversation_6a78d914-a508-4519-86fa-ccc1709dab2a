{% extends 'base.html' %}

{% block title %}Edit {{ part.name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-edit me-2"></i>Edit {{ part.name }}</h1>
                <div>
                    <a href="{{ url_for('inventory_enhanced.part_detail', part_id=part.id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Part
                    </a>
                </div>
            </div>

            <!-- Edit Part Form -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cog me-2"></i>Part Information</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Part Name *</label>
                                    <input type="text" class="form-control" name="name" value="{{ part.name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="part_number" class="form-label">Part Number *</label>
                                    <input type="text" class="form-control" name="part_number" value="{{ part.part_number }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category *</label>
                                    <input type="text" class="form-control" name="category" value="{{ part.category }}" list="categories" required>
                                    <datalist id="categories">
                                        {% for cat in categories %}
                                        <option value="{{ cat }}">
                                        {% endfor %}
                                    </datalist>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="brand" class="form-label">Brand</label>
                                    <input type="text" class="form-control" name="brand" value="{{ part.brand or '' }}" list="brands">
                                    <datalist id="brands">
                                        {% for brand in brands %}
                                        <option value="{{ brand }}">
                                        {% endfor %}
                                    </datalist>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" name="description" rows="3">{{ part.description or '' }}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Price *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" name="price" step="0.01" min="0" value="{{ part.price }}" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="min_quantity" class="form-label">Minimum Stock Level</label>
                                    <input type="number" class="form-control" name="min_quantity" min="0" value="{{ part.min_quantity }}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="location" class="form-label">Storage Location</label>
                                    <input type="text" class="form-control" name="location" value="{{ part.location or '' }}" placeholder="e.g., Shelf A-1, Bin 23">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="supplier" class="form-label">Supplier</label>
                                    <input type="text" class="form-control" name="supplier" value="{{ part.supplier or '' }}">
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Current Stock:</strong> {{ part.quantity }} units
                            <br>
                            <small>To modify stock levels, use the stock management section in the part details page.</small>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ url_for('inventory_enhanced.part_detail', part_id=part.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0 !important;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.alert-info {
    background-color: #e3f2fd;
    border-color: #bbdefb;
    color: #0d47a1;
}
</style>
{% endblock %}
