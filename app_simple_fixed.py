#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 Device Repair Management System - Simplified Version
======================================================
SQLite-only version without external dependencies
"""

import os
import sys
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'device-repair-system-2025-secure-key'

# Database configuration - SQLite only
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{os.path.join(basedir, "instance", "simple_repair.db")}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Ensure instance directory exists
os.makedirs(os.path.join(basedir, 'instance'), exist_ok=True)

# Initialize extensions
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'

# User model
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    full_name = db.Column(db.String(100))
    role = db.Column(db.String(20), default='admin')  # admin, technician, customer
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

# Device model
class Device(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    type = db.Column(db.String(100), nullable=False)
    model = db.Column(db.String(100), nullable=False)
    color = db.Column(db.String(50))
    serial_number = db.Column(db.String(100))
    owner_name = db.Column(db.String(100))
    owner_phone = db.Column(db.String(20))
    status = db.Column(db.String(20), default='received')  # received, in_repair, repaired, delivered
    problem_description = db.Column(db.Text)
    repair_notes = db.Column(db.Text)
    cost = db.Column(db.Float, default=0.0)
    payment_status = db.Column(db.String(20), default='pending')  # pending, paid, partial
    technician_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship
    technician = db.relationship('User', backref='assigned_devices')

# Spare Part model
class SparePart(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    part_number = db.Column(db.String(100), unique=True, nullable=False)
    category = db.Column(db.String(100), nullable=False)
    brand = db.Column(db.String(100))
    quantity = db.Column(db.Integer, default=0)
    min_quantity = db.Column(db.Integer, default=5)
    price = db.Column(db.Float, nullable=False)
    cost = db.Column(db.Float, default=0.0)
    location = db.Column(db.String(100))
    supplier = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    @property
    def is_low_stock(self):
        return self.quantity <= self.min_quantity

    @property
    def stock_status(self):
        if self.quantity == 0:
            return 'Out of Stock'
        elif self.is_low_stock:
            return 'Low Stock'
        else:
            return 'In Stock'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Initialize database and create default users
def init_database():
    """Initialize database and create default users"""
    with app.app_context():
        db.create_all()
        
        # Create default admin user if not exists
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='System Administrator',
                role='admin',
                is_active=True
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            
        # Create default technician user if not exists
        tech_user = User.query.filter_by(username='technician').first()
        if not tech_user:
            tech_user = User(
                username='technician',
                email='<EMAIL>',
                full_name='Default Technician',
                role='technician',
                is_active=True
            )
            tech_user.set_password('tech123')
            db.session.add(tech_user)
            
        db.session.commit()
        print("✅ Database initialized with default users")
        print("   Admin: admin / admin123")
        print("   Technician: technician / tech123")

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('simple_index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user)
            flash('Login successful!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid username or password', 'error')
    
    return render_template('simple_login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get statistics
    total_devices = Device.query.count()
    received_count = Device.query.filter_by(status='received').count()
    in_repair_count = Device.query.filter_by(status='in_repair').count()
    repaired_count = Device.query.filter_by(status='repaired').count()
    delivered_count = Device.query.filter_by(status='delivered').count()
    
    # Get recent devices
    recent_devices = Device.query.order_by(Device.created_at.desc()).limit(10).all()
    
    # Get low stock items
    low_stock_items = SparePart.query.filter(SparePart.quantity <= SparePart.min_quantity).all()
    
    return render_template('simple_dashboard.html',
                         total_devices=total_devices,
                         received_count=received_count,
                         in_repair_count=in_repair_count,
                         repaired_count=repaired_count,
                         delivered_count=delivered_count,
                         recent_devices=recent_devices,
                         low_stock_items=low_stock_items)

@app.route('/devices')
@login_required
def devices():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status_filter = request.args.get('status', '')

    query = Device.query

    if search:
        query = query.filter(
            (Device.type.contains(search)) |
            (Device.model.contains(search)) |
            (Device.owner_name.contains(search)) |
            (Device.serial_number.contains(search))
        )

    if status_filter:
        query = query.filter_by(status=status_filter)

    devices = query.order_by(Device.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('simple_devices.html', devices=devices, search=search, status_filter=status_filter)

@app.route('/devices/add', methods=['GET', 'POST'])
@login_required
def add_device():
    if request.method == 'POST':
        device = Device(
            type=request.form['type'],
            model=request.form['model'],
            color=request.form.get('color', ''),
            serial_number=request.form.get('serial_number', ''),
            owner_name=request.form['owner_name'],
            owner_phone=request.form.get('owner_phone', ''),
            problem_description=request.form.get('problem_description', ''),
            technician_id=request.form.get('technician_id') or None
        )

        db.session.add(device)
        db.session.commit()

        flash('Device added successfully!', 'success')
        return redirect(url_for('devices'))

    technicians = User.query.filter_by(role='technician').all()
    return render_template('simple_add_device.html', technicians=technicians)

@app.route('/devices/<int:device_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_device(device_id):
    device = Device.query.get_or_404(device_id)

    if request.method == 'POST':
        device.type = request.form['type']
        device.model = request.form['model']
        device.color = request.form.get('color', '')
        device.serial_number = request.form.get('serial_number', '')
        device.owner_name = request.form['owner_name']
        device.owner_phone = request.form.get('owner_phone', '')
        device.problem_description = request.form.get('problem_description', '')
        device.repair_notes = request.form.get('repair_notes', '')
        device.status = request.form['status']
        device.cost = float(request.form.get('cost', 0))
        device.payment_status = request.form['payment_status']
        device.technician_id = request.form.get('technician_id') or None
        device.updated_at = datetime.utcnow()

        db.session.commit()
        flash('Device updated successfully!', 'success')
        return redirect(url_for('devices'))

    technicians = User.query.filter_by(role='technician').all()
    return render_template('simple_edit_device.html', device=device, technicians=technicians)

@app.route('/spare-parts')
@login_required
def spare_parts():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category_filter = request.args.get('category', '')

    query = SparePart.query

    if search:
        query = query.filter(
            (SparePart.name.contains(search)) |
            (SparePart.part_number.contains(search)) |
            (SparePart.brand.contains(search))
        )

    if category_filter:
        query = query.filter_by(category=category_filter)

    parts = query.order_by(SparePart.name).paginate(
        page=page, per_page=20, error_out=False
    )

    categories = db.session.query(SparePart.category).distinct().all()
    categories = [cat[0] for cat in categories if cat[0]]

    return render_template('simple_spare_parts.html', parts=parts, search=search,
                         category_filter=category_filter, categories=categories)

@app.route('/spare-parts/add', methods=['GET', 'POST'])
@login_required
def add_spare_part():
    if request.method == 'POST':
        part = SparePart(
            name=request.form['name'],
            part_number=request.form['part_number'],
            category=request.form['category'],
            brand=request.form.get('brand', ''),
            quantity=int(request.form.get('quantity', 0)),
            min_quantity=int(request.form.get('min_quantity', 5)),
            price=float(request.form['price']),
            cost=float(request.form.get('cost', 0)),
            location=request.form.get('location', ''),
            supplier=request.form.get('supplier', '')
        )

        db.session.add(part)
        db.session.commit()

        flash('Spare part added successfully!', 'success')
        return redirect(url_for('spare_parts'))

    return render_template('simple_add_spare_part.html')

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('simple_404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('simple_500.html'), 500

if __name__ == '__main__':
    # Initialize database
    init_database()

    print("\n" + "="*60)
    print(" 🔧 DEVICE REPAIR MANAGEMENT SYSTEM - SIMPLIFIED")
    print("="*60)
    print(" 🚀 Server Status: STARTING")
    print(f" ⏰ Server Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(" 🗄️  Database: SQLite")
    print(" 🌐 URL: http://127.0.0.1:5000")
    print("="*60)
    print(" 👤 Default Users:")
    print("   Admin: admin / admin123")
    print("   Technician: technician / tech123")
    print("="*60 + "\n")

    # Run the application
    app.run(host='127.0.0.1', port=5000, debug=True)
