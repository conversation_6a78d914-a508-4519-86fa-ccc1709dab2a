from app.models.user import User, ROLE_ADMIN, ROLE_TECHNICIAN, ROLE_CUSTOMER
from app.models.device import Device, UsedPart
from app.models.repair_ticket import RepairTicket
from app.models.spare_part import SparePart, StockMovement, RepairPartUsage
from app.models.settings import SystemSettings
from app.models.tenant import Tenant
from app.models.subscription import SubscriptionPlan
from app.models.payment import Payment
from app.models.customer import Customer
from app.models.authorized_staff import AuthorizedStaff
from app.models.notification import TelegramConfig, NotificationLog, WhatsAppConfig
from app.models.notification_settings_simple import NotificationSettings, UserNotificationPreferences
from app.models.billing import Invoice, UsageRecord
from app.models.spare_parts_enhanced import (
    SaleTransaction, SaleTransactionItem, InventoryAudit,
    ReorderAlert, InventoryValuation, SalesReport
)

# Create alias for backward compatibility
InventoryItem = SparePart

__all__ = [
    'User', 'Device', 'UsedPart', 'RepairTicket', 'SystemSettings', 'Activation',
    'SparePart', 'InventoryItem', 'StockMovement', 'RepairPartUsage',
    'SaleTransaction', 'SaleTransactionItem', 'InventoryAudit',
    'ReorderAlert', 'InventoryValuation', 'SalesReport',
    'ROLE_ADMIN', 'ROLE_TECHNICIAN', 'ROLE_CUSTOMER',
    'AuthorizedStaff', 'Customer', 'Invoice', 'UsageRecord',
    'TelegramConfig', 'NotificationLog', 'WhatsAppConfig',
    'NotificationSettings', 'UserNotificationPreferences',
    'Tenant', 'SubscriptionPlan', 'Payment'
]