from datetime import datetime, timedelta
from app.extensions import db
import uuid

class Invoice(db.Model):
    """
    Invoice model for billing management
    """
    __tablename__ = 'invoice'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenant.id'), nullable=False, index=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('tenant_subscription.id'), nullable=False)
    
    # Invoice details
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, paid, overdue, cancelled
    
    # Amounts
    subtotal = db.Column(db.Float, default=0.0)
    tax_amount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, default=0.0)
    currency = db.Column(db.String(3), default='USD')
    
    # Dates
    issue_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime, nullable=False)
    paid_date = db.Column(db.DateTime, nullable=True)
    
    # Billing period
    period_start = db.Column(db.DateTime, nullable=False)
    period_end = db.Column(db.DateTime, nullable=False)
    
    # Payment details
    payment_method = db.Column(db.String(50), nullable=True)
    payment_reference = db.Column(db.String(100), nullable=True)
    
    # Metadata
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships - using string references to avoid circular imports
    tenant = db.relationship('Tenant', backref=db.backref('invoices', lazy=True))
    subscription = db.relationship('TenantSubscription', backref=db.backref('invoices', lazy=True))
    
    def __init__(self, **kwargs):
        super(Invoice, self).__init__(**kwargs)
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()
        if not self.due_date and self.issue_date:
            self.due_date = self.issue_date + timedelta(days=30)
    
    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'
    
    @staticmethod
    def generate_invoice_number():
        """Generate unique invoice number"""
        timestamp = datetime.utcnow().strftime('%Y%m%d')
        unique_id = str(uuid.uuid4())[:8].upper()
        return f"INV-{timestamp}-{unique_id}"
    
    def is_overdue(self):
        """Check if invoice is overdue"""
        return self.status == 'pending' and datetime.utcnow() > self.due_date
    
    def days_overdue(self):
        """Get number of days overdue"""
        if self.is_overdue():
            delta = datetime.utcnow() - self.due_date
            return delta.days
        return 0
    
    def mark_as_paid(self, payment_method=None, payment_reference=None):
        """Mark invoice as paid"""
        self.status = 'paid'
        self.paid_date = datetime.utcnow()
        if payment_method:
            self.payment_method = payment_method
        if payment_reference:
            self.payment_reference = payment_reference
    
    def calculate_totals(self, tax_rate=0.0):
        """Calculate invoice totals"""
        self.tax_amount = self.subtotal * tax_rate
        self.total_amount = self.subtotal + self.tax_amount
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'tenant_id': self.tenant_id,
            'subscription_id': self.subscription_id,
            'invoice_number': self.invoice_number,
            'status': self.status,
            'subtotal': self.subtotal,
            'tax_amount': self.tax_amount,
            'total_amount': self.total_amount,
            'currency': self.currency,
            'issue_date': self.issue_date.isoformat() if self.issue_date else None,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'paid_date': self.paid_date.isoformat() if self.paid_date else None,
            'period_start': self.period_start.isoformat() if self.period_start else None,
            'period_end': self.period_end.isoformat() if self.period_end else None,
            'payment_method': self.payment_method,
            'payment_reference': self.payment_reference,
            'is_overdue': self.is_overdue(),
            'days_overdue': self.days_overdue(),
            'notes': self.notes
        }

class UsageRecord(db.Model):
    """
    Usage tracking for billing purposes
    """
    __tablename__ = 'usage_record'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    tenant_id = db.Column(db.Integer, db.ForeignKey('tenant.id'), nullable=False, index=True)
    
    # Usage metrics
    metric_name = db.Column(db.String(50), nullable=False)  # devices, technicians, customers, storage
    metric_value = db.Column(db.Integer, default=0)
    metric_limit = db.Column(db.Integer, nullable=True)
    
    # Time period
    recorded_date = db.Column(db.DateTime, default=datetime.utcnow)
    period_start = db.Column(db.DateTime, nullable=False)
    period_end = db.Column(db.DateTime, nullable=False)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships - using string reference to avoid circular imports
    tenant = db.relationship('Tenant', backref=db.backref('usage_records', lazy=True))
    
    def __repr__(self):
        return f'<UsageRecord {self.tenant_id}:{self.metric_name}={self.metric_value}>'
    
    def is_over_limit(self):
        """Check if usage is over the limit"""
        if self.metric_limit is None:
            return False
        return self.metric_value > self.metric_limit
    
    def usage_percentage(self):
        """Get usage as percentage of limit"""
        if self.metric_limit is None or self.metric_limit == 0:
            return 0
        return min(100, (self.metric_value / self.metric_limit) * 100)
    
    @classmethod
    def record_usage(cls, tenant_id, metric_name, metric_value, metric_limit=None):
        """Record usage for a tenant"""
        now = datetime.utcnow()
        period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # Calculate period end (last day of month)
        if period_start.month == 12:
            period_end = period_start.replace(year=period_start.year + 1, month=1) - timedelta(days=1)
        else:
            period_end = period_start.replace(month=period_start.month + 1) - timedelta(days=1)
        period_end = period_end.replace(hour=23, minute=59, second=59)
        
        # Check if record already exists for this period
        existing = cls.query.filter_by(
            tenant_id=tenant_id,
            metric_name=metric_name,
            period_start=period_start
        ).first()
        
        if existing:
            existing.metric_value = metric_value
            existing.metric_limit = metric_limit
            existing.recorded_date = now
            return existing
        else:
            record = cls(
                tenant_id=tenant_id,
                metric_name=metric_name,
                metric_value=metric_value,
                metric_limit=metric_limit,
                period_start=period_start,
                period_end=period_end
            )
            db.session.add(record)
            return record
    
    def to_dict(self):
        """Convert to dictionary for API responses"""
        return {
            'id': self.id,
            'tenant_id': self.tenant_id,
            'metric_name': self.metric_name,
            'metric_value': self.metric_value,
            'metric_limit': self.metric_limit,
            'recorded_date': self.recorded_date.isoformat() if self.recorded_date else None,
            'period_start': self.period_start.isoformat() if self.period_start else None,
            'period_end': self.period_end.isoformat() if self.period_end else None,
            'usage_percentage': self.usage_percentage(),
            'is_over_limit': self.is_over_limit()
        }
