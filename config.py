import os
from datetime import timedelta

class Config:
    # Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'device-repair-system-2025-secure'

    # Database configuration - SQLite Only
    DATABASE_TYPE = 'sqlite'

    def __init__(self):
        """Initialize configuration and set engine options"""
        self.SQLALCHEMY_ENGINE_OPTIONS = {
            'pool_pre_ping': True,
            'pool_recycle': 300,
            'echo': False,
            'connect_args': {
                'check_same_thread': False,
                'timeout': 20
            }
        }

    # SQLite Database URI
    @property
    def SQLALCHEMY_DATABASE_URI(self):
        """SQLite database URI"""
        return 'sqlite:///' + os.path.join(os.path.abspath(os.path.dirname(__file__)), 'instance', 'app.db')
    

    
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(days=1)

    # Application configuration
    APP_NAME = 'Device Repair Management System'
    APP_URL = os.environ.get('APP_URL', 'http://localhost:5000')

    # Application features
    TRIAL_DAYS = 14
    MAX_DEVICES = 1000  # Unlimited for SQLite version
    MAX_TECHNICIANS = 10  # Reasonable limit for SQLite

    # Logging configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', 'app.log')

    @staticmethod
    def init_app(app):
        """Initialize application configuration."""
        import logging
        from logging.handlers import RotatingFileHandler

        handler = RotatingFileHandler(
            Config.LOG_FILE,
            maxBytes=10000000,  # 10MB
            backupCount=10
        )
        handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s '
            '[in %(pathname)s:%(lineno)d]'
        ))
        handler.setLevel(logging.INFO)
        app.logger.addHandler(handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('Device Repair Management System startup')

class DevelopmentConfig(Config):
    DEBUG = True
    SQLALCHEMY_ECHO = False
    SESSION_COOKIE_SECURE = False

    def __init__(self):
        super().__init__()

class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

    def __init__(self):
        super().__init__()
        # Override engine options for testing
        self.SQLALCHEMY_ENGINE_OPTIONS = {
            'pool_pre_ping': True,
            'echo': False
        }

class ProductionConfig(Config):
    DEBUG = False
    SQLALCHEMY_ECHO = False

    def __init__(self):
        super().__init__()

config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
