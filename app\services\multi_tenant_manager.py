"""
Multi-Tenant Database Manager
Manages separate databases for each tenant/user
"""

import os
import sqlite3
import shutil
import logging
from datetime import datetime
from flask import current_app
from app.extensions import db
from app.models.user import User
from app.models.customer import Customer
from app.models.device import Device
from app.models.spare_part import SparePart, StockMovement
from app.models.support_chat import SupportChat

# Setup logging
tenant_logger = logging.getLogger('multi_tenant')

class MultiTenantManager:
    """Manager for multi-tenant database operations"""
    
    def __init__(self):
        self.base_db_path = 'databases'
        self.template_db_path = 'template_database.db'
        self.master_db_path = 'master_tenants.db'
        
        # Ensure directories exist
        os.makedirs(self.base_db_path, exist_ok=True)
        
        tenant_logger.info("Multi-tenant manager initialized")
    
    def create_template_database(self):
        """Create template database with default data"""
        try:
            template_path = os.path.join(self.base_db_path, self.template_db_path)
            
            # Create template database
            conn = sqlite3.connect(template_path)
            cursor = conn.cursor()
            
            # Create all tables
            self._create_tables(cursor)
            
            # Add default spare parts
            self._add_default_spare_parts(cursor)
            
            # Add default settings
            self._add_default_settings(cursor)
            
            conn.commit()
            conn.close()
            
            tenant_logger.info(f"Template database created: {template_path}")
            return True
            
        except Exception as e:
            tenant_logger.error(f"Failed to create template database: {e}")
            return False
    
    def _create_tables(self, cursor):
        """Create all necessary tables"""
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(80) UNIQUE NOT NULL,
                email VARCHAR(120) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'admin',
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME,
                tenant_id INTEGER DEFAULT 1,
                trial_start_date DATETIME,
                trial_end_date DATETIME,
                is_trial_account BOOLEAN DEFAULT 0,
                trial_expired BOOLEAN DEFAULT 0,
                license_type VARCHAR(20) DEFAULT 'trial'
            )
        ''')
        
        # Customers table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customer (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20) UNIQUE NOT NULL,
                email VARCHAR(120),
                company VARCHAR(100),
                address TEXT,
                notes TEXT,
                telegram_chat_id VARCHAR(50),
                telegram_username VARCHAR(100),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                tenant_id INTEGER DEFAULT 1
            )
        ''')
        
        # Devices table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS device (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type VARCHAR(50) NOT NULL,
                model VARCHAR(100) NOT NULL,
                serial_number VARCHAR(100),
                issue_description TEXT NOT NULL,
                status VARCHAR(20) DEFAULT 'received',
                estimated_cost DECIMAL(10,2),
                actual_cost DECIMAL(10,2),
                customer_id INTEGER NOT NULL,
                technician_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME,
                is_soft_deleted BOOLEAN DEFAULT 0,
                tenant_id INTEGER DEFAULT 1,
                FOREIGN KEY (customer_id) REFERENCES customer (id),
                FOREIGN KEY (technician_id) REFERENCES user (id)
            )
        ''')
        
        # Spare Parts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS spare_parts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                part_number VARCHAR(50) UNIQUE NOT NULL,
                category VARCHAR(50) NOT NULL,
                brand VARCHAR(50),
                model_compatibility TEXT,
                quantity_in_stock INTEGER DEFAULT 0,
                minimum_stock_level INTEGER DEFAULT 0,
                maximum_stock_level INTEGER DEFAULT 0,
                cost_price DECIMAL(10,2) DEFAULT 0.00,
                selling_price DECIMAL(10,2) DEFAULT 0.00,
                storage_location VARCHAR(100),
                shelf_number VARCHAR(20),
                bin_location VARCHAR(20),
                supplier_name VARCHAR(100),
                supplier_contact VARCHAR(100),
                condition VARCHAR(20) DEFAULT 'new',
                quality_grade VARCHAR(10),
                warranty_period INTEGER DEFAULT 0,
                description TEXT,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                tenant_id INTEGER DEFAULT 1
            )
        ''')
        
        # Stock Movements table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_movement (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                spare_part_id INTEGER NOT NULL,
                movement_type VARCHAR(10) NOT NULL,
                quantity INTEGER NOT NULL,
                previous_quantity INTEGER NOT NULL,
                new_quantity INTEGER NOT NULL,
                reference_type VARCHAR(50),
                reference_id INTEGER,
                reason TEXT,
                unit_cost DECIMAL(10,2),
                total_cost DECIMAL(10,2),
                created_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                tenant_id INTEGER DEFAULT 1,
                FOREIGN KEY (spare_part_id) REFERENCES spare_part (id),
                FOREIGN KEY (created_by) REFERENCES user (id)
            )
        ''')
        
        # Support Chat table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS support_chat (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id VARCHAR(100) UNIQUE NOT NULL,
                telegram_chat_id VARCHAR(50),
                user_name VARCHAR(100),
                user_email VARCHAR(120),
                user_phone VARCHAR(20),
                company_name VARCHAR(100),
                status VARCHAR(20) DEFAULT 'active',
                priority VARCHAR(20) DEFAULT 'normal',
                category VARCHAR(50) DEFAULT 'general',
                subject VARCHAR(200),
                initial_message TEXT,
                assigned_agent VARCHAR(100),
                agent_telegram_id VARCHAR(50),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                closed_at DATETIME,
                tenant_id INTEGER NOT NULL DEFAULT 1
            )
        ''')
        
        # Support Messages table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS support_message (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                chat_id INTEGER NOT NULL,
                sender_type VARCHAR(20) NOT NULL,
                sender_name VARCHAR(100),
                message TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_read BOOLEAN DEFAULT 0,
                tenant_id INTEGER DEFAULT 1,
                FOREIGN KEY (chat_id) REFERENCES support_chat (id)
            )
        ''')
        
        # Tenant Settings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tenant_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key VARCHAR(100) NOT NULL,
                setting_value TEXT,
                tenant_id INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
    
    def _add_default_spare_parts(self, cursor):
        """Add default spare parts from the comprehensive inventory list"""
        try:
            # Import the comprehensive inventory list
            from app.data.default_inventory import ALL_DEFAULT_INVENTORY

            print(f"🔄 Adding {len(ALL_DEFAULT_INVENTORY)} comprehensive spare parts...")

            for item in ALL_DEFAULT_INVENTORY:
                name, category, quantity, cost_price, supplier, description = item
                part_number = f"SP-{category[:3].upper()}-{len(name)}{hash(name) % 1000:03d}"
                
                cursor.execute('''
                    INSERT INTO spare_part (
                        name, part_number, category, quantity_in_stock,
                        cost_price, supplier_name, description,
                        minimum_stock_level, maximum_stock_level,
                        condition, quality_grade, warranty_period
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    name, part_number, category, quantity,
                    cost_price, supplier, description,
                    2, 20,  # Default min/max stock levels
                    'new', 'A', 30  # Default values
                ))

                # Add initial stock movement if quantity > 0
                if quantity > 0:
                    spare_part_id = cursor.lastrowid
                    cursor.execute('''
                        INSERT INTO stock_movement (
                            spare_part_id, movement_type, quantity,
                            previous_quantity, new_quantity,
                            reference_type, reason,
                            unit_cost, total_cost
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        spare_part_id, 'IN', quantity,
                        0, quantity,
                        'INITIAL_STOCK', 'Initial stock entry',
                        cost_price, cost_price * quantity
                    ))

            print("✅ Default spare parts added successfully")
            
        except ImportError:
            print("⚠️ Could not import default inventory data")
            pass
        except Exception as e:
            print(f"❌ Error adding default spare parts: {e}")
            raise
    
    def _add_default_settings(self, cursor):
        """Add default tenant settings"""
        default_settings = [
            ('company_name', 'ورشة إصلاح الأجهزة'),
            ('company_phone', '+966501234567'),
            ('company_email', '<EMAIL>'),
            ('company_address', 'الرياض، المملكة العربية السعودية'),
            ('telegram_bot_token', ''),
            ('telegram_support_group_id', ''),
            ('whatsapp_enabled', 'false'),
            ('telegram_enabled', 'false'),
            ('default_currency', 'SAR'),
            ('working_hours', 'السبت - الخميس: 9:00 ص - 9:00 م'),
            ('repair_warranty_days', '30'),
            ('auto_notifications', 'true')
        ]
        
        for key, value in default_settings:
            cursor.execute('''
                INSERT INTO tenant_settings (setting_key, setting_value, tenant_id)
                VALUES (?, ?, 1)
            ''', (key, value))
    
    def create_tenant_database(self, tenant_id, tenant_name):
        """Create new database for tenant"""
        try:
            # Create tenant database filename
            db_filename = f"tenant_{tenant_id}_{tenant_name.lower().replace(' ', '_')}.db"
            tenant_db_path = os.path.join(self.base_db_path, db_filename)
            
            # Copy template database
            template_path = os.path.join(self.base_db_path, self.template_db_path)
            
            if not os.path.exists(template_path):
                self.create_template_database()
            
            shutil.copy2(template_path, tenant_db_path)
            
            # Update tenant_id in all tables
            conn = sqlite3.connect(tenant_db_path)
            cursor = conn.cursor()
            
            # Update tenant_id in all relevant tables
            tables_to_update = [
                'user', 'customer', 'device', 'spare_part', 
                'stock_movement', 'support_chat', 'support_message', 'tenant_settings'
            ]
            
            for table in tables_to_update:
                cursor.execute(f'UPDATE {table} SET tenant_id = ?', (tenant_id,))
            
            # Update company name in settings
            cursor.execute('''
                UPDATE tenant_settings 
                SET setting_value = ? 
                WHERE setting_key = 'company_name' AND tenant_id = ?
            ''', (tenant_name, tenant_id))
            
            conn.commit()
            conn.close()
            
            tenant_logger.info(f"Created tenant database: {tenant_db_path}")
            return db_filename
            
        except Exception as e:
            tenant_logger.error(f"Failed to create tenant database: {e}")
            return None
    
    def get_tenant_database_path(self, tenant_id):
        """Get database path for tenant"""
        try:
            # Find database file for tenant
            for filename in os.listdir(self.base_db_path):
                if filename.startswith(f"tenant_{tenant_id}_") and filename.endswith('.db'):
                    return os.path.join(self.base_db_path, filename)
            
            return None
            
        except Exception as e:
            tenant_logger.error(f"Failed to get tenant database path: {e}")
            return None
    
    def switch_to_tenant_database(self, tenant_id):
        """Switch current database connection to tenant database"""
        try:
            tenant_db_path = self.get_tenant_database_path(tenant_id)
            
            if not tenant_db_path:
                tenant_logger.error(f"No database found for tenant {tenant_id}")
                return False
            
            # Update Flask-SQLAlchemy database URI
            current_app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{tenant_db_path}'
            
            # Recreate database connection
            db.engine.dispose()
            db.create_all()
            
            tenant_logger.info(f"Switched to tenant {tenant_id} database: {tenant_db_path}")
            return True
            
        except Exception as e:
            tenant_logger.error(f"Failed to switch to tenant database: {e}")
            return False
    
    def clean_current_database(self):
        """Clean current database of test data"""
        try:
            # Delete test devices
            Device.query.delete()
            
            # Delete test customers (keep structure)
            Customer.query.delete()
            
            # Delete test support chats
            SupportChat.query.delete()
            
            # Reset inventory quantities to 0 but keep items
            spare_parts = SparePart.query.all()
            for item in spare_parts:
                item.quantity_in_stock = 0
                item.cost_price = 0.00
            
            db.session.commit()
            
            tenant_logger.info("Cleaned current database of test data")
            return True
            
        except Exception as e:
            tenant_logger.error(f"Failed to clean database: {e}")
            db.session.rollback()
            return False
    
    def register_tenant(self, tenant_name, admin_username, admin_email, admin_password):
        """Register new tenant with admin user"""
        try:
            # Generate tenant ID
            tenant_id = int(datetime.now().timestamp())
            
            # Create tenant database
            db_filename = self.create_tenant_database(tenant_id, tenant_name)
            
            if not db_filename:
                return None, "Failed to create tenant database"
            
            # Switch to tenant database
            if not self.switch_to_tenant_database(tenant_id):
                return None, "Failed to switch to tenant database"
            
            # Create admin user in tenant database
            from werkzeug.security import generate_password_hash
            
            admin_user = User(
                username=admin_username,
                email=admin_email,
                password_hash=generate_password_hash(admin_password),
                role='admin',
                tenant_id=tenant_id,
                is_active=True
            )
            
            db.session.add(admin_user)
            db.session.commit()
            
            tenant_logger.info(f"Registered new tenant: {tenant_name} (ID: {tenant_id})")
            
            return {
                'tenant_id': tenant_id,
                'tenant_name': tenant_name,
                'database_file': db_filename,
                'admin_user_id': admin_user.id
            }, None
            
        except Exception as e:
            tenant_logger.error(f"Failed to register tenant: {e}")
            db.session.rollback()
            return None, str(e)
    
    def list_tenants(self):
        """List all registered tenants"""
        try:
            tenants = []
            
            for filename in os.listdir(self.base_db_path):
                if filename.startswith('tenant_') and filename.endswith('.db'):
                    # Extract tenant info from filename
                    parts = filename.replace('.db', '').split('_')
                    if len(parts) >= 3:
                        tenant_id = parts[1]
                        tenant_name = '_'.join(parts[2:]).replace('_', ' ').title()
                        
                        tenants.append({
                            'tenant_id': tenant_id,
                            'tenant_name': tenant_name,
                            'database_file': filename
                        })
            
            return tenants
            
        except Exception as e:
            tenant_logger.error(f"Failed to list tenants: {e}")
            return []
