#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 DEVICE REPAIR MANAGEMENT SYSTEM - COMPLETE STARTUP SCRIPT
============================================================
Comprehensive startup script that handles all dependencies and configurations
"""

import os
import sys
import subprocess
import sqlite3
from datetime import datetime

def print_header():
    """Print startup header"""
    print("=" * 70)
    print(" 🚀 DEVICE REPAIR MANAGEMENT SYSTEM - STARTUP")
    print("=" * 70)
    print(f" ⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(" 🎯 Goal: Start the enhanced system with all improvements")
    print("=" * 70)

def check_python_version():
    """Check Python version"""
    print("\n🐍 Checking Python Version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing Dependencies...")
    
    # Essential dependencies (SQLite only)
    essential_deps = [
        'flask',
        'flask-sqlalchemy',
        'flask-login',
        'flask-wtf',
        'python-dotenv',
        'flask-babel',
        'flask-migrate'
    ]
    
    failed_installs = []
    
    for dep in essential_deps:
        try:
            print(f"   Installing {dep}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', dep
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print(f"   ✅ {dep} installed successfully")
            else:
                print(f"   ❌ {dep} installation failed")
                failed_installs.append(dep)
                
        except subprocess.TimeoutExpired:
            print(f"   ⏰ {dep} installation timed out")
            failed_installs.append(dep)
        except Exception as e:
            print(f"   ❌ {dep} installation error: {e}")
            failed_installs.append(dep)
    
    if failed_installs:
        print(f"\n⚠️ Failed to install: {', '.join(failed_installs)}")
        print("   You may need to install these manually:")
        for dep in failed_installs:
            print(f"   pip install {dep}")
        return False
    
    print("✅ All dependencies installed successfully!")
    return True

def check_database():
    """Check database connectivity"""
    print("\n🗄️ Checking Database...")
    
    # Ensure instance directory exists
    os.makedirs('instance', exist_ok=True)
    
    try:
        conn = sqlite3.connect('instance/app.db')
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        conn.close()
        
        print(f"✅ SQLite Database: {len(tables)} tables found")
        return True
    except Exception as e:
        print(f"❌ Database Error: {e}")
        return False

def check_files():
    """Check required files exist"""
    print("\n📁 Checking Required Files...")
    
    required_files = [
        'app/__init__.py',
        'app/routes/inventory_enhanced.py',
        'app/templates/activate.html',
        'app/templates/inventory/enhanced_index.html',
        'config.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - Missing")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ Missing files: {len(missing_files)}")
        return False
    
    print("✅ All required files present!")
    return True

def test_import():
    """Test if the app can be imported"""
    print("\n🔍 Testing App Import...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.getcwd())
        
        from app import create_app
        app = create_app()
        
        print("✅ App import successful!")
        print(f"   App name: {app.name}")
        print(f"   Debug mode: {app.debug}")
        
        return app
    except Exception as e:
        print(f"❌ App import failed: {e}")
        return None

def start_app(app):
    """Start the Flask application"""
    print("\n🚀 Starting Application...")
    
    try:
        print("\n" + "=" * 70)
        print(" 🎯 ENHANCED DEVICE REPAIR MANAGEMENT SYSTEM")
        print("=" * 70)
        print(" 🌐 Main Application: http://127.0.0.1:5000")
        print(" 📦 Enhanced Inventory: http://127.0.0.1:5000/inventory")
        print(" 💰 Enhanced Accounting: http://127.0.0.1:5000/accounting")
        print(" 🔓 Open Access Mode: No activation required")
        print("=" * 70)
        print(" 🎉 All improvements are active!")
        print("=" * 70 + "\n")
        
        # Start the app
        app.run(host='127.0.0.1', port=5000, debug=True)
        
    except Exception as e:
        print(f"❌ Failed to start app: {e}")
        return False
    
    return True

def main():
    """Main startup function"""
    print_header()
    
    # Step 1: Check Python version
    if not check_python_version():
        print("\n❌ Python version incompatible. Please upgrade to Python 3.8+")
        return 1
    
    # Step 2: Check required files
    if not check_files():
        print("\n❌ Missing required files. Please ensure all files are present.")
        return 1
    
    # Step 3: Install dependencies
    print("\n🤔 Do you want to install/update dependencies? (y/n): ", end="")
    try:
        response = input().lower().strip()
        if response in ['y', 'yes', '']:
            if not install_dependencies():
                print("\n⚠️ Some dependencies failed to install, but continuing...")
    except KeyboardInterrupt:
        print("\n\n❌ Installation cancelled by user")
        return 1
    
    # Step 4: Check database
    if not check_database():
        print("\n❌ Database check failed")
        return 1
    
    # Step 5: Test app import
    app = test_import()
    if not app:
        print("\n❌ App import failed. Please check the error messages above.")
        print("\n💡 Try installing dependencies manually:")
        print("   pip install flask flask-sqlalchemy flask-login flask-wtf python-dotenv")
        return 1
    
    # Step 6: Start the application
    print("\n🚀 Ready to start! Press Ctrl+C to stop the server.")
    print("   Starting in 3 seconds...")
    
    try:
        import time
        time.sleep(3)
        start_app(app)
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n\n👋 Startup cancelled by user")
        sys.exit(0)
