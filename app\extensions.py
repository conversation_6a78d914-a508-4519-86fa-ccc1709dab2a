from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager
from datetime import timedelta

# Core extensions (always required)
db = SQLAlchemy()
login_manager = LoginManager()

# Optional extensions (graceful fallback)
try:
    from flask_migrate import Migrate
    migrate = Migrate()
except ImportError:
    print("Warning: Flask-Migrate not available. Database migrations disabled.")
    migrate = None

try:
    from flask_wtf.csrf import CSRFProtect
    csrf = CSRFProtect()
except ImportError:
    print("Warning: Flask-WTF not available. CSRF protection disabled.")
    csrf = None